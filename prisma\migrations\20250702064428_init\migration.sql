-- CreateTable
CREATE TABLE "User" (
    "Id" UUID NOT NULL,
    "Name" TEXT,
    "Email" TEXT NOT NULL,
    "Password" TEXT NOT NULL,
    "IsActive" BOOLEAN NOT NULL DEFAULT true,
    "IsVerified" BOOLEAN NOT NULL DEFAULT false,
    "RefreshToken" TEXT,
    "TokenExpiry" TIMESTAMP(3),
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,
    "LastLoginAt" TIMESTAMP(3),

    CONSTRAINT "User_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Company" (
    "Id" UUID NOT NULL,
    "UserId" UUID NOT NULL,
    "Name" VARCHAR(150) NOT NULL,
    "XeroTenantId" VARCHAR(100),
    "XeroAccessToken" TEXT,
    "XeroRefreshToken" TEXT,
    "XeroTokenExpiry" TIMESTAMP(3),
    "XeroRefreshTokenExpiry" TIMESTAMP(3),
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Company_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Account" (
    "Id" UUID NOT NULL,
    "BankAccountType" VARCHAR(50),
    "BankAccountNumber" VARCHAR(50),
    "Code" VARCHAR(10),
    "AccountClassTypes" VARCHAR(50),
    "Type" VARCHAR(50),
    "Name" VARCHAR(150),
    "Description" TEXT,
    "ReportingCode" VARCHAR(200),
    "ReportingCodeName" VARCHAR(500),
    "CurrencyCode" VARCHAR(50),
    "TaxType" VARCHAR(50),
    "SystemAccount" VARCHAR(50),
    "Status" VARCHAR(50),
    "EnablePaymentsToAccount" BOOLEAN,
    "ShowInExpenseClaims" BOOLEAN,
    "UpdateUtcDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Account_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "BankTransaction" (
    "BankTransactionID" UUID NOT NULL,
    "Type" VARCHAR(20),
    "AccountID" UUID NOT NULL,
    "BankAccountNumber" VARCHAR(50),
    "Code" VARCHAR(10),
    "BankAccountName" VARCHAR(150),
    "ContactID" UUID,
    "ContactName" VARCHAR(255),
    "ContactFirstName" VARCHAR(255),
    "ContactLastName" VARCHAR(255),
    "CurrencyCode" VARCHAR(3),
    "Date" DATE,
    "DueDate" DATE,
    "FullyPaidOnDate" DATE,
    "IsReconciled" BOOLEAN,
    "LineAmountTypes" VARCHAR(10),
    "Reference" VARCHAR(255),
    "Status" VARCHAR(10),
    "SubTotal" DECIMAL(18,2),
    "TotalTax" DECIMAL(18,2),
    "Total" DECIMAL(18,2),
    "TotalMovement" DECIMAL(18,2),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,
    "CurrencyRate" DECIMAL(18,4),

    CONSTRAINT "BankTransaction_pkey" PRIMARY KEY ("BankTransactionID")
);

-- CreateTable
CREATE TABLE "BankTransactionLine" (
    "Id" UUID NOT NULL,
    "BankTransactionId" UUID NOT NULL,
    "LineItemId" UUID NOT NULL,
    "ContactId" UUID,
    "ContactName" VARCHAR(255),
    "Date" DATE,
    "Reference" VARCHAR(255),
    "Description" TEXT,
    "Quantity" DECIMAL(18,2),
    "UnitAmount" DECIMAL(18,2),
    "AccountCode" VARCHAR(10),
    "TaxAmount" DECIMAL(18,2),
    "LineAmount" DECIMAL(18,2),
    "TaxType" VARCHAR(30),
    "TrackingCategory1" TEXT,
    "TrackingCategory1Value" TEXT,
    "TrackingCategory2" TEXT,
    "TrackingCategory2Value" TEXT,
    "CurrencyCode" VARCHAR(20),
    "CurrencyRate" DECIMAL(18,4),
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "BankTransactionLine_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "BankTransfer" (
    "BankTransferID" UUID NOT NULL,
    "FromBankAccountAccountID" UUID,
    "FromBankAccountName" VARCHAR(255),
    "ToBankAccountAccountID" UUID,
    "ToBankAccountName" VARCHAR(255),
    "Amount" DECIMAL(18,2),
    "FromBankTransactionID" UUID,
    "ToBankTransactionID" UUID,
    "FromIsReconciled" BOOLEAN,
    "ToIsReconciled" BOOLEAN,
    "CurrencyRate" DECIMAL(18,4),
    "Reference" VARCHAR(50),
    "CreatedDateUTC" DATE,
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "BankTransfer_pkey" PRIMARY KEY ("BankTransferID")
);

-- CreateTable
CREATE TABLE "Budget" (
    "BudgetID" UUID NOT NULL,
    "BudgetType" VARCHAR(50),
    "Description" VARCHAR(255),
    "Trackings" INTEGER,
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Budget_pkey" PRIMARY KEY ("BudgetID")
);

-- CreateTable
CREATE TABLE "Contact" (
    "ContactID" UUID NOT NULL,
    "ContactNumber" VARCHAR(50),
    "ContactStatus" VARCHAR(10),
    "AccountNumber" VARCHAR(50),
    "Name" VARCHAR(255),
    "FirstName" VARCHAR(255),
    "LastName" VARCHAR(255),
    "EmailAddress" VARCHAR(255),
    "SkypeUserName" VARCHAR(255),
    "DefaultCurrency" VARCHAR(50),
    "BankAccountDetails" VARCHAR(50),
    "TaxNumber" VARCHAR(50),
    "AccountsReceivableTaxType" VARCHAR(50),
    "AccountsPayableTaxType" VARCHAR(50),
    "IsSupplier" BOOLEAN,
    "IsCustomer" BOOLEAN,
    "PurchasesDefaultAccountCode" VARCHAR(50),
    "SalesDefaultAccountCode" VARCHAR(50),
    "BatchPaymentsBankAccountNumber" VARCHAR(30),
    "BatchPaymentsBankAccountName" VARCHAR(30),
    "BatchPaymentsDetails" TEXT,
    "AccountsReceivableOutstanding" DECIMAL(18,2),
    "AccountsReceivableOverdue" DECIMAL(18,2),
    "AccountsPayableOutstanding" DECIMAL(18,2),
    "AccountsPayableOverdue1" DECIMAL(18,2),
    "PhoneDefaultNumber" VARCHAR(50),
    "PhoneDefaultAreaCode" VARCHAR(10),
    "PhoneDefaultCountryCode" VARCHAR(20),
    "PhoneDDINumber" VARCHAR(50),
    "PhoneDDIAreaCode" VARCHAR(10),
    "PhoneDDICountryCode" VARCHAR(20),
    "PhoneMobileNumber" VARCHAR(50),
    "PhoneMobileAreaCode" VARCHAR(10),
    "PhoneMobileCountryCode" VARCHAR(20),
    "PhoneFaxNumber" VARCHAR(50),
    "PhoneFaxAreaCode" VARCHAR(10),
    "PhoneFaxCountryCode" VARCHAR(20),
    "MailingAddressAttensionTo" VARCHAR(255),
    "MailingAddressAddressLine1" VARCHAR(500),
    "MailingAddressAddressLine2" VARCHAR(500),
    "MailingAddressAddressLine3" VARCHAR(500),
    "MailingAddressAddressLine4" VARCHAR(500),
    "MailingAddressCity" VARCHAR(255),
    "MailingAddressCountry" VARCHAR(50),
    "MailingAddressPostalCode" VARCHAR(50),
    "MailingAddressRegion" VARCHAR(255),
    "StreetAttensionTo" VARCHAR(255),
    "StreetAddressLine1" VARCHAR(500),
    "StreetAddressLine2" VARCHAR(500),
    "StreetAddressLine3" VARCHAR(500),
    "StreetAddressLine4" VARCHAR(500),
    "StreetCity" VARCHAR(255),
    "StreetCountry" VARCHAR(50),
    "StreetPostalCode" VARCHAR(50),
    "StreetRegion" VARCHAR(255),
    "ContactGroups" VARCHAR(500),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Contact_pkey" PRIMARY KEY ("ContactID")
);

-- CreateTable
CREATE TABLE "CreditNote" (
    "CreditNoteID" UUID NOT NULL,
    "Type" VARCHAR(15),
    "CreditNoteNumber" VARCHAR(20),
    "ContactId" UUID,
    "ContactName" VARCHAR(255),
    "ContactFirstName" VARCHAR(255),
    "ContactLastName" VARCHAR(255),
    "Date" DATE,
    "DueDate" DATE,
    "FullyPaidOnDate" DATE,
    "LineAmountType" VARCHAR(20),
    "Reference" VARCHAR(50),
    "Status" VARCHAR(10),
    "AmountDue" DECIMAL(18,2),
    "AmountPaid" DECIMAL(18,2),
    "AmountAllocated" DECIMAL(18,2),
    "AmountDueToDate" DECIMAL(18,2),
    "AmountPaidToDate" DECIMAL(18,2),
    "AmountAllocatedToDate" DECIMAL(18,2),
    "RemainingCredit" DECIMAL(18,2),
    "CurrencyCode" VARCHAR(3),
    "CurrencyRate" DECIMAL(18,4),
    "SubTotal" DECIMAL(18,2),
    "TotalTax" DECIMAL(18,2),
    "Total" DECIMAL(18,2),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "CreditNote_pkey" PRIMARY KEY ("CreditNoteID")
);

-- CreateTable
CREATE TABLE "CreditNoteLine" (
    "Id" UUID NOT NULL,
    "CreditNoteId" UUID NOT NULL,
    "LineItemId" UUID,
    "CreditNoteNumber" VARCHAR(10),
    "ContactId" UUID,
    "ContactName" VARCHAR(255),
    "Date" DATE,
    "Reference" VARCHAR(50),
    "ItemCode" VARCHAR(30),
    "Description" TEXT,
    "Quantity" DECIMAL(18,2),
    "UnitAmount" DECIMAL(18,2),
    "UnitCost" DECIMAL(18,2),
    "AccountCode" VARCHAR(10),
    "TaxAmount" DECIMAL(18,2),
    "TaxType" VARCHAR(30),
    "LineAmount" DECIMAL(18,2),
    "TrackingCategory1" TEXT,
    "TrackingCategory1Value" TEXT,
    "TrackingCategory2" TEXT,
    "TrackingCategory2Value" TEXT,
    "CurrencyCode" VARCHAR(20),
    "CurrencyRate" DECIMAL(18,4),
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "CreditNoteLine_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Currency" (
    "Code" VARCHAR(3) NOT NULL,
    "Description" VARCHAR(100),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Currency_pkey" PRIMARY KEY ("Code")
);

-- CreateTable
CREATE TABLE "Employee" (
    "EmployeeID" UUID NOT NULL,
    "Status" VARCHAR(50),
    "FirstName" VARCHAR(255),
    "LastName" VARCHAR(255),
    "ExternalLinkUrl" VARCHAR(255),
    "ExternalLinkDescription" TEXT,
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Employee_pkey" PRIMARY KEY ("EmployeeID")
);

-- CreateTable
CREATE TABLE "ExpenseClaim" (
    "ExpenseClaimID" VARCHAR(50) NOT NULL,
    "Status" VARCHAR(50),
    "UserID" UUID,
    "EmailAddress" TEXT,
    "FirstName" TEXT,
    "LastName" TEXT,
    "UserUpdatedDateUTC" TIMESTAMP(6),
    "IsSubscriber" BOOLEAN,
    "OrganisationRole" VARCHAR(50),
    "Total" DECIMAL(18,2),
    "AmountDue" DECIMAL(18,2),
    "AmountPaid" DECIMAL(18,2),
    "PaymentDueDate" TIMESTAMP(6),
    "ReportingDate" TIMESTAMP(6),
    "UpdateUTCDate" TIMESTAMP(6),
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "ExpenseClaim_pkey" PRIMARY KEY ("ExpenseClaimID")
);

-- CreateTable
CREATE TABLE "Invoice" (
    "InvoiceID" UUID NOT NULL,
    "Type" VARCHAR(20),
    "InvoiceNumber" TEXT,
    "ContactID" UUID,
    "ContactName" VARCHAR(255),
    "FirstName" VARCHAR(255),
    "LastName" VARCHAR(255),
    "Date" DATE,
    "DueDate" DATE,
    "FullyPaidOnDate" DATE,
    "ExpectedPaymentDate" DATE,
    "PlannedPaymentDate" DATE,
    "LineAmountTypes" VARCHAR(9),
    "Reference" VARCHAR(255),
    "Status" VARCHAR(10),
    "HasAttachments" BOOLEAN,
    "SentToContact" BOOLEAN,
    "BrandingThemeID" UUID,
    "BrandingThemeName" VARCHAR(30),
    "AmountDue" DECIMAL(18,2),
    "AmountPaid" DECIMAL(18,2),
    "AmountCredited" DECIMAL(18,2),
    "AmountPaidToDate" DECIMAL(18,2),
    "AmountDueToDate" DECIMAL(18,2),
    "AmountCreditedToDate" DECIMAL(18,2),
    "CurrencyCode" VARCHAR(3),
    "CurrencyRate" DECIMAL(18,4),
    "SubTotal" DECIMAL(18,2),
    "TotalDiscount" DECIMAL(18,2),
    "TotalTax" DECIMAL(18,2),
    "Total" DECIMAL(18,2),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Invoice_pkey" PRIMARY KEY ("InvoiceID")
);

-- CreateTable
CREATE TABLE "InvoiceLine" (
    "Id" UUID NOT NULL,
    "InvoiceID" UUID NOT NULL,
    "InvoiceNumber" TEXT,
    "ContactID" UUID,
    "ContactName" VARCHAR(255),
    "Date" DATE,
    "Reference" VARCHAR(255),
    "LineItemID" UUID NOT NULL,
    "ItemCode" VARCHAR(30),
    "Description" TEXT,
    "Quantity" DECIMAL(18,2),
    "UnitAmount" DECIMAL(18,2),
    "UnitCost" DECIMAL(18,2),
    "DiscountRate" DECIMAL(18,2),
    "AccountCode" VARCHAR(10),
    "AccountID" UUID,
    "TaxAmount" DECIMAL(18,2),
    "TaxType" VARCHAR(30),
    "LineAmountTypes" VARCHAR(10),
    "LineAmount" DECIMAL(18,2),
    "LineAmountIncl" DECIMAL(18,2),
    "LineAmountExcl" DECIMAL(18,2),
    "CompanyId" UUID NOT NULL,
    "TrackingCategory1" VARCHAR(100),
    "TrackingCategory1Value" VARCHAR(100),
    "TrackingCategory2" VARCHAR(100),
    "TrackingCategory2Value" VARCHAR(100),
    "CurrencyCode" VARCHAR(20),
    "CurrencyRate" DECIMAL(18,4),

    CONSTRAINT "InvoiceLine_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Item" (
    "ItemID" UUID NOT NULL,
    "Name" VARCHAR(50),
    "Code" VARCHAR(30),
    "Description" TEXT,
    "PurchaseDescription" TEXT,
    "IsPurchased" BOOLEAN,
    "IsSold" BOOLEAN,
    "IsTrackedAsInventory" BOOLEAN,
    "TotalCostPool" DECIMAL(18,2),
    "QuantityOnHand" DECIMAL(18,2),
    "InventoryAssetAccountCode" VARCHAR(50),
    "PurchaseAccountCode" VARCHAR(10),
    "PurchaseTaxType" VARCHAR(10),
    "PurchaseUnitPrice" DECIMAL(18,2),
    "COGSAccountCode" VARCHAR(10),
    "SalesTaxType" VARCHAR(50),
    "SalesAccountCode" VARCHAR(10),
    "SalesUnitPrice" DECIMAL(18,2),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Item_pkey" PRIMARY KEY ("ItemID")
);

-- CreateTable
CREATE TABLE "Journal" (
    "JournalID" UUID NOT NULL,
    "JournalNumber" INTEGER,
    "CreatedDateUTC" DATE,
    "JournalDate" DATE,
    "Reference" VARCHAR(255),
    "SourceType" VARCHAR(30),
    "SourceID" VARCHAR(150),
    "TotalGrossAmount" DECIMAL(18,2),
    "TotalNetAmount" DECIMAL(18,2),
    "TotalTaxAmount" DECIMAL(18,2),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Journal_pkey" PRIMARY KEY ("JournalID")
);

-- CreateTable
CREATE TABLE "JournalLine" (
    "Id" UUID NOT NULL,
    "JournalID" UUID NOT NULL,
    "JournalNumber" INTEGER,
    "JournalDate" DATE,
    "Reference" VARCHAR(255),
    "JournalLineID" UUID NOT NULL,
    "SourceType" VARCHAR(30),
    "SourceID" VARCHAR(150),
    "AccountID" UUID,
    "AccountCode" VARCHAR(10),
    "AccountType" VARCHAR(50),
    "AccountName" VARCHAR(50),
    "Description" TEXT,
    "NetAmount" DECIMAL(18,2),
    "GrossAmount" DECIMAL(18,2),
    "Debit" DECIMAL(18,2),
    "Credit" DECIMAL(18,2),
    "TaxAmount" DECIMAL(18,2),
    "TaxType" VARCHAR(30),
    "TaxName" VARCHAR(60),
    "CompanyId" UUID NOT NULL,
    "TrackingCategory1" VARCHAR(100),
    "TrackingCategory1Value" VARCHAR(100),
    "TrackingCategory2" VARCHAR(100),
    "TrackingCategory2Value" VARCHAR(100),
    "Status" VARCHAR(20) NOT NULL,

    CONSTRAINT "JournalLine_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "ManualJournal" (
    "ManualJournalID" UUID NOT NULL,
    "Date" DATE,
    "Status" VARCHAR(50),
    "LineAmountTypes" VARCHAR(50),
    "Narration" VARCHAR(255),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "ManualJournal_pkey" PRIMARY KEY ("ManualJournalID")
);

-- CreateTable
CREATE TABLE "ManualJournalLine" (
    "Id" UUID NOT NULL,
    "ManualJournalID" UUID NOT NULL,
    "Description" TEXT,
    "TaxType" VARCHAR(50),
    "TaxAmount" DECIMAL(18,2),
    "Debit" DECIMAL(18,2),
    "Credit" DECIMAL(18,2),
    "AccountCode" VARCHAR(50),
    "AccountID" UUID NOT NULL,
    "Region" VARCHAR(100),
    "TrackingCategory1" VARCHAR(100),
    "TrackingCategory1Value" VARCHAR(100),
    "TrackingCategory2" VARCHAR(100),
    "TrackingCategory2Value" VARCHAR(100),
    "IsBlank" BOOLEAN,
    "IsRecentUpdatedInTable" BOOLEAN NOT NULL,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "ManualJournalLine_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "Organisation" (
    "OrganisationID" UUID NOT NULL,
    "Name" VARCHAR(150),
    "LegalName" VARCHAR(150),
    "ShortCode" VARCHAR(50),
    "OrganisationType" VARCHAR(50),
    "CountryCode" VARCHAR(50),
    "BaseCurrency" VARCHAR(50),
    "IsDemoCompany" BOOLEAN,
    "FinancialYearEndDay" INTEGER,
    "FinancialYearEndMonth" INTEGER,
    "EndOfYearLockDate" DATE,
    "PeriodLockDate" DATE,
    "PaysTax" BOOLEAN,
    "TaxNumber" VARCHAR(50),
    "Timezone" VARCHAR(50),
    "Version" VARCHAR(50),
    "MailingAddressAttensionTo" VARCHAR(255),
    "MailingAddressAddressLine1" VARCHAR(500),
    "MailingAddressAddressLine2" VARCHAR(500),
    "MailingAddressAddressLine3" VARCHAR(500),
    "MailingAddressAddressLine4" VARCHAR(500),
    "MailingAddressCity" VARCHAR(255),
    "MailingAddressCountry" VARCHAR(50),
    "MailingAddressPostalCode" VARCHAR(50),
    "MailingAddressRegion" VARCHAR(255),
    "StreetAttensionTo" VARCHAR(255),
    "StreetAddressLine1" VARCHAR(500),
    "StreetAddressLine2" VARCHAR(500),
    "StreetAddressLine3" VARCHAR(500),
    "StreetAddressLine4" VARCHAR(500),
    "StreetCity" VARCHAR(255),
    "StreetCountry" VARCHAR(50),
    "StreetPostalCode" VARCHAR(50),
    "StreetRegion" VARCHAR(255),
    "PhoneDefaultNumber" VARCHAR(50),
    "PhoneDefaultAreaCode" VARCHAR(50),
    "PhoneDefaultCountryCode" VARCHAR(50),
    "PhoneDDINumber" VARCHAR(50),
    "PhoneDDIAreaCode" VARCHAR(50),
    "PhoneDDICountryCode" VARCHAR(50),
    "PhoneMobileNumber" VARCHAR(50),
    "PhoneMobileAreaCode" VARCHAR(50),
    "PhoneMobileCountryCode" VARCHAR(50),
    "PhoneFaxNumber" VARCHAR(50),
    "PhoneFaxAreaCode" VARCHAR(50),
    "PhoneFaxCountryCode" VARCHAR(50),
    "PhoneOfficeNumber" VARCHAR(50),
    "PhoneOfficeAreaCode" VARCHAR(50),
    "PhoneOfficeCountryCode" VARCHAR(50),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Organisation_pkey" PRIMARY KEY ("OrganisationID")
);

-- CreateTable
CREATE TABLE "Payment" (
    "PaymentID" UUID NOT NULL,
    "PaymentType" VARCHAR(30),
    "Date" TIMESTAMP(6),
    "AccountID" UUID NOT NULL,
    "BankAccountNumber" VARCHAR(50),
    "AccountCode" VARCHAR(10),
    "AccountName" VARCHAR(150),
    "BankAmount" DECIMAL(18,2),
    "Amount" DECIMAL(18,2),
    "CurrencyRate" DECIMAL(18,4),
    "InvoiceID" UUID,
    "InvoiceNumber" TEXT,
    "InvoiceTotal" DECIMAL(18,2),
    "Reference" VARCHAR(255),
    "Status" VARCHAR(10),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Payment_pkey" PRIMARY KEY ("PaymentID")
);

-- CreateTable
CREATE TABLE "Receipt" (
    "ReceiptsID" VARCHAR(50) NOT NULL,
    "ReceiptNumber" VARCHAR(50),
    "Status" VARCHAR(50),
    "UserID" VARCHAR(50),
    "FirstName" TEXT,
    "LastName" TEXT,
    "ContactID" VARCHAR(50),
    "ContactName" TEXT,
    "Date" TIMESTAMP(6),
    "Reference" TEXT,
    "LineAmountTypes" VARCHAR(50),
    "SubTotal" DECIMAL(18,2),
    "TotalTax" DECIMAL(18,2),
    "Total" DECIMAL(18,2),
    "HasAttachments" BOOLEAN,
    "UpdateUTCDate" TIMESTAMP(6),
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "Receipt_pkey" PRIMARY KEY ("ReceiptsID")
);

-- CreateTable
CREATE TABLE "TaxRate" (
    "Id" UUID NOT NULL,
    "Name" TEXT,
    "TaxType" VARCHAR(50),
    "ReportTaxType" VARCHAR(50),
    "CanApplyToAssets" BOOLEAN,
    "CanApplyToEquity" BOOLEAN,
    "CanApplyToExpenses" BOOLEAN,
    "CanApplyToLiabilities" BOOLEAN,
    "CanApplyToRevenue" BOOLEAN,
    "DisplayTaxRate" DECIMAL(18,2),
    "EffectiveRate" DECIMAL(18,2),
    "Status" VARCHAR(50),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "TaxRate_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "TaxRateLine" (
    "Id" UUID NOT NULL,
    "TaxRateName" TEXT,
    "TaxComponentsName" TEXT,
    "Rate" DECIMAL(18,2),
    "IsCompound" BOOLEAN,
    "IsNonRecoverable" BOOLEAN,
    "CompanyId" UUID NOT NULL,
    "IsRecentUpdatedInTable" BOOLEAN NOT NULL,

    CONSTRAINT "TaxRateLine_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "TrackingCategory" (
    "TrackingCategoryID" UUID NOT NULL,
    "CategoryName" VARCHAR(100),
    "TotalOptions" INTEGER,
    "Status" VARCHAR(50),
    "UpdateUTCDate" DATE,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "TrackingCategory_pkey" PRIMARY KEY ("TrackingCategoryID")
);

-- CreateTable
CREATE TABLE "TrackingCategoryLine" (
    "Id" UUID NOT NULL,
    "TrackingCategoryID" UUID NOT NULL,
    "TrackingOptionID" UUID NOT NULL,
    "Status" VARCHAR(50),
    "Name" VARCHAR(255),
    "CompanyId" UUID NOT NULL,
    "IsRecentUpdatedInTable" BOOLEAN NOT NULL,

    CONSTRAINT "TrackingCategoryLine_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "ApiLog" (
    "Id" TEXT NOT NULL,
    "UserId" UUID,
    "CompanyId" UUID NOT NULL,
    "Method" VARCHAR(20),
    "ApiUrl" TEXT,
    "Status" VARCHAR(20),
    "IntegrationName" VARCHAR(100),
    "Duration" VARCHAR(20),
    "ApiName" VARCHAR(100) NOT NULL,
    "ApiRequest" JSONB,
    "ApiResponse" JSONB,
    "IsActive" BOOLEAN NOT NULL DEFAULT true,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "ApiLog_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "SyncLog" (
    "Id" UUID NOT NULL,
    "Entity" VARCHAR(100) NOT NULL,
    "LastSync" TIMESTAMP(3) NOT NULL,
    "Status" VARCHAR(50) NOT NULL,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "SyncLog_pkey" PRIMARY KEY ("Id")
);

-- CreateIndex
CREATE UNIQUE INDEX "User_Email_key" ON "User"("Email");

-- AddForeignKey
ALTER TABLE "Company" ADD CONSTRAINT "Company_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Account" ADD CONSTRAINT "Account_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankTransaction" ADD CONSTRAINT "BankTransaction_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankTransactionLine" ADD CONSTRAINT "BankTransactionLine_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankTransactionLine" ADD CONSTRAINT "BankTransactionLine_BankTransactionId_fkey" FOREIGN KEY ("BankTransactionId") REFERENCES "BankTransaction"("BankTransactionID") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BankTransfer" ADD CONSTRAINT "BankTransfer_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Budget" ADD CONSTRAINT "Budget_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Contact" ADD CONSTRAINT "Contact_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CreditNote" ADD CONSTRAINT "CreditNote_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CreditNoteLine" ADD CONSTRAINT "CreditNoteLine_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CreditNoteLine" ADD CONSTRAINT "CreditNoteLine_CreditNoteId_fkey" FOREIGN KEY ("CreditNoteId") REFERENCES "CreditNote"("CreditNoteID") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Currency" ADD CONSTRAINT "Currency_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Employee" ADD CONSTRAINT "Employee_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ExpenseClaim" ADD CONSTRAINT "ExpenseClaim_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Invoice" ADD CONSTRAINT "Invoice_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvoiceLine" ADD CONSTRAINT "InvoiceLine_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "InvoiceLine" ADD CONSTRAINT "InvoiceLine_InvoiceID_fkey" FOREIGN KEY ("InvoiceID") REFERENCES "Invoice"("InvoiceID") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Item" ADD CONSTRAINT "Item_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Journal" ADD CONSTRAINT "Journal_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JournalLine" ADD CONSTRAINT "JournalLine_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "JournalLine" ADD CONSTRAINT "JournalLine_JournalID_fkey" FOREIGN KEY ("JournalID") REFERENCES "Journal"("JournalID") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualJournal" ADD CONSTRAINT "ManualJournal_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualJournalLine" ADD CONSTRAINT "ManualJournalLine_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ManualJournalLine" ADD CONSTRAINT "ManualJournalLine_ManualJournalID_fkey" FOREIGN KEY ("ManualJournalID") REFERENCES "ManualJournal"("ManualJournalID") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Organisation" ADD CONSTRAINT "Organisation_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Payment" ADD CONSTRAINT "Payment_InvoiceID_fkey" FOREIGN KEY ("InvoiceID") REFERENCES "Invoice"("InvoiceID") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Receipt" ADD CONSTRAINT "Receipt_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TaxRate" ADD CONSTRAINT "TaxRate_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TaxRateLine" ADD CONSTRAINT "TaxRateLine_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrackingCategory" ADD CONSTRAINT "TrackingCategory_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrackingCategoryLine" ADD CONSTRAINT "TrackingCategoryLine_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "TrackingCategoryLine" ADD CONSTRAINT "TrackingCategoryLine_TrackingCategoryID_fkey" FOREIGN KEY ("TrackingCategoryID") REFERENCES "TrackingCategory"("TrackingCategoryID") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiLog" ADD CONSTRAINT "ApiLog_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ApiLog" ADD CONSTRAINT "ApiLog_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "SyncLog" ADD CONSTRAINT "SyncLog_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;
