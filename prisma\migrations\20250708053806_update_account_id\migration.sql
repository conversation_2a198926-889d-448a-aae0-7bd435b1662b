/*
  Warnings:

  - The primary key for the `Account` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - You are about to drop the column `Id` on the `Account` table. All the data in the column will be lost.
  - The required column `AccountID` was added to the `Account` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.

*/
-- AlterTable
ALTER TABLE "Account" DROP CONSTRAINT "Account_pkey",
DROP COLUMN "Id",
ADD COLUMN     "AccountID" UUID NOT NULL,
ADD CONSTRAINT "Account_pkey" PRIMARY KEY ("AccountID");
