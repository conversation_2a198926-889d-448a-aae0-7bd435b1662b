/**
 * @fileoverview Sync Log Routes
 * @description Express routes for sync logging and monitoring functionality.
 * Provides endpoints for viewing sync history, detailed logs, retry operations,
 * and sync statistics.
 *
 * Routes:
 * - GET /companies/:companyId/sync-logs - Get paginated sync logs with filtering
 * - GET /sync-logs/:syncLogId - Get detailed sync log by ID
 * - POST /sync-logs/:syncLogId/retry - Retry a failed sync operation
 * - GET /companies/:companyId/sync-stats - Get sync statistics for a company
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-01
 */

import { Router } from 'express';
import {
  getSyncLogsController,
  getSyncLogDetailsController,
  retrySyncController,
  getSyncStatsController,
} from '@controllers/syncLog.controller';
import { authenticate } from '@middlewares/auth.middleware';

const router = Router();

/**
 * @route GET /api/v1/companies/:companyId/sync-logs
 * @desc Get sync logs for a company with filtering and pagination
 * @access Private (requires authentication)
 * @param {string} companyId - Company UUID
 * @query {string} [startDate] - Filter logs from this date (ISO string)
 * @query {string} [endDate] - Filter logs until this date (ISO string)
 * @query {string} [entity] - Filter by entity type (Accounts, Invoices, etc.)
 * @query {string} [status] - Filter by sync status (PENDING, SUCCESS, ERROR, etc.)
 * @query {string} [integration] - Filter by integration name (Xero, QuickBooks, etc.)
 * @query {number} [limit=50] - Number of results per page (1-100)
 * @query {number} [offset=0] - Number of results to skip
 * @example
 * GET /api/v1/companies/123e4567-e89b-12d3-a456-************/sync-logs?entity=Accounts&status=SUCCESS&limit=20
 * @returns {Object} 200 - Success response with sync logs
 * @returns {Object} 400 - Bad request (invalid parameters)
 * @returns {Object} 401 - Unauthorized
 * @returns {Object} 404 - Company not found
 * @returns {Object} 500 - Internal server error
 */
router.get('/companies/:companyId/sync-logs', authenticate, getSyncLogsController);

/**
 * @route GET /api/v1/sync-logs/:syncLogId
 * @desc Get detailed information about a specific sync log
 * @access Private (requires authentication)
 * @param {string} syncLogId - Sync log UUID
 * @example
 * GET /api/v1/sync-logs/123e4567-e89b-12d3-a456-************
 * @returns {Object} 200 - Success response with detailed sync log
 * @returns {Object} 400 - Bad request (invalid sync log ID)
 * @returns {Object} 401 - Unauthorized
 * @returns {Object} 404 - Sync log not found
 * @returns {Object} 500 - Internal server error
 */
router.get('/sync-logs/:syncLogId', authenticate, getSyncLogDetailsController);

/**
 * @route POST /api/v1/sync-logs/:syncLogId/retry
 * @desc Manually retry a failed sync operation
 * @access Private (requires authentication)
 * @param {string} syncLogId - Sync log UUID to retry
 * @example
 * POST /api/v1/sync-logs/123e4567-e89b-12d3-a456-************/retry
 * @returns {Object} 200 - Success response with retry confirmation
 * @returns {Object} 400 - Bad request (invalid sync log ID or retry not allowed)
 * @returns {Object} 401 - Unauthorized
 * @returns {Object} 404 - Sync log not found
 * @returns {Object} 409 - Conflict (sync in progress or max retries exceeded)
 * @returns {Object} 500 - Internal server error
 */
router.post('/sync-logs/:syncLogId/retry', authenticate, retrySyncController);

/**
 * @route GET /api/v1/companies/:companyId/sync-stats
 * @desc Get aggregated sync statistics for a company
 * @access Private (requires authentication)
 * @param {string} companyId - Company UUID
 * @example
 * GET /api/v1/companies/123e4567-e89b-12d3-a456-************/sync-stats
 * @returns {Object} 200 - Success response with sync statistics
 * @returns {Object} 400 - Bad request (invalid company ID)
 * @returns {Object} 401 - Unauthorized
 * @returns {Object} 404 - Company not found
 * @returns {Object} 500 - Internal server error
 */
router.get('/companies/:companyId/sync-stats', authenticate, getSyncStatsController);

export default router;
