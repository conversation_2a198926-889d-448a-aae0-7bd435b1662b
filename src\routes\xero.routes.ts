import { Router } from 'express';
import {
  generateAuthUrl,
  xeroCallback,
  xeroDisconnect,
  refreshXeroToken,
} from '@controllers/xero.controller';
import { authenticate } from '@middlewares/auth.middleware';

// initialize a new router instance
const router = Router();

/**
 * Xero OAuth2 routes:
 *
 * - /connect: generate Xero authorization URL
 * - /callback: Xero redirects here after successful login
 * - /disconnect: removes Xero tokens from Company
 * - /refresh: manually refreshes Xero tokens
 */

// Generate Xero OAuth2 authorization URL
router.get('/connect', authenticate, generateAuthUrl);

// Handle OAuth2 callback from Xero
router.get('/callback', authenticate, xeroCallback);

// Disconnect a Xero connection
router.post('/disconnect', authenticate, xeroDisconnect);

// Refresh Xero token manually
router.post('/refresh', authenticate, refreshXeroToken);

export default router;
