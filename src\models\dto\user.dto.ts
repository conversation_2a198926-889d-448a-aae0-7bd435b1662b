/**
 * DTO for user registration request
 * Used for validating incoming registration data from clients
 */
export interface UserRegisterDTO {
  /** Optional user display name */
  name?: string;
  /** User email address - must be unique and valid */
  email: string;
  /** User password - will be hashed before storage */
  password: string;
}

/**
 * DTO for user login request
 * Used for validating incoming login credentials
 */
export interface UserLoginDTO {
  /** User email address */
  email: string;
  /** User password in plaintext */
  password: string;
}

/**
 * DTO for user responses (returned to clients)
 * Safe user data without sensitive information like passwords
 */
export interface UserResponseDTO {
  /** User unique identifier */
  id: string;
  /** User display name */
  name?: string;
  /** User email address */
  email: string;
  /** JWT authentication token (included in login responses) */
  token?: string;
  /** Token expiration date (included in login responses) */
  expiresAt?: Date;
}

/**
 * Repository interface for user creation
 * Matches the database schema field names
 */
export interface UserCreateData {
  /** User display name (optional) */
  Name?: string;
  /** User email address (required, unique) */
  Email: string;
  /** Hashed password (required) */
  Password: string;
  /** User active status (defaults to true) */
  IsActive?: boolean;
  /** User verification status (defaults to false) */
  IsVerified?: boolean;
}

/**
 * Repository interface for user updates
 * All fields are optional for partial updates
 */
export interface UserUpdateData {
  /** User display name */
  Name?: string;
  /** User email address */
  Email?: string;
  /** Hashed password */
  Password?: string;
  /** User active status */
  IsActive?: boolean;
  /** User verification status */
  IsVerified?: boolean;
  /** JWT refresh token */
  RefreshToken?: string | null;
  /** Token expiry timestamp */
  TokenExpiry?: Date | null;
  /** Last login timestamp */
  LastLoginAt?: Date;
}

/**
 * Database user entity interface
 * Represents the complete user record from database
 */
export interface UserEntity {
  /** User unique identifier */
  Id: string;
  /** User display name */
  Name: string | null;
  /** User email address */
  Email: string;
  /** Hashed password */
  Password: string;
  /** User active status */
  IsActive: boolean;
  /** User verification status */
  IsVerified: boolean;
  /** JWT refresh token */
  RefreshToken: string | null;
  /** Token expiry timestamp */
  TokenExpiry: Date | null;
  /** Record creation timestamp */
  CreatedAt: Date;
  /** Record last update timestamp */
  UpdatedAt: Date;
  /** Last login timestamp */
  LastLoginAt: Date | null;
}

/**
 * Safe user entity interface (without sensitive data)
 * Used for returning user data to clients
 */
export interface SafeUserEntity {
  /** User unique identifier */
  Id: string;
  /** User display name */
  Name: string | null;
  /** User email address */
  Email: string;
  /** User active status */
  IsActive: boolean;
  /** User verification status */
  IsVerified: boolean;
  /** Record creation timestamp */
  CreatedAt: Date;
  /** Record last update timestamp */
  UpdatedAt: Date;
  /** Last login timestamp */
  LastLoginAt: Date | null;
}
