import dotenv from 'dotenv';
import { PrismaClient, Prisma } from '@prisma/client';
import { z } from 'zod';

// Load environment variables
dotenv.config();

// Environment validation schema
const envSchema = z.object({
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('8000'),
  HOST: z.string().default('localhost'),

  // Database
  DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),

  // JWT Configuration
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('1h'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),

  // CORS Configuration
  CORS_ORIGIN: z.string().default('http://localhost:8080'),
  CORS_CREDENTIALS: z
    .string()
    .transform((val: string) => val === 'true')
    .default('true'),

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'),
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),

  // Logging
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  LOG_FILE_ERROR: z.string().optional(),
  LOG_FILE_COMBINED: z.string().optional(),

  // Security
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  SESSION_SECRET: z.string().optional(),

  // File Upload
  MAX_FILE_SIZE: z.string().transform(Number).default('5242880'), // 5MB
  UPLOAD_PATH: z.string().default('uploads/'),

  // API Configuration
  API_VERSION: z.string().default('v1'),
  API_PREFIX: z.string().default('/api'),
  XERO_CLIENT_ID: z.string().min(1, 'XERO_CLIENT_ID is required'),
  XERO_CLIENT_SECRET: z.string().min(1, 'XERO_CLIENT_SECRET is required'),
  XERO_REDIRECT_URI: z.string().url('XERO_REDIRECT_URI must be a valid URL'),
  XERO_AUTH_URL: z.string().url('XERO_AUTH_URL must be a valid URL'),
  XERO_TOKEN_URL: z.string().url('XERO_TOKEN_URL must be a valid URL'),
  XERO_REVOKE_URL: z.string().url('XERO_API_BASE_URL must be a valid URL'),

  LAMBDA_SYNC_BANK_TRANSACTIONS_URL: z.string().min(1, 'LAMBDA_SYNC_BANK_TRANSACTIONS_URL is required'),
  LAMBDA_SYNC_BANK_TRANSFERS_URL: z.string().min(1, 'LAMBDA_SYNC_BANK_TRANSFERS_URL is required'),
  LAMBDA_SYNC_CREDIT_NOTES_URL: z.string().min(1, 'LAMBDA_SYNC_CREDIT_NOTES_URL is required'),
  LAMBDA_SYNC_EMPLOYEES_URL: z.string().min(1, 'LAMBDA_SYNC_EMPLOYEES_URL is required'),
  LAMBDA_SYNC_EXPENSE_CLAIMS_URL: z.string().min(1, 'LAMBDA_SYNC_EXPENSE_CLAIMS_URL is required'),
  LAMBDA_SYNC_INVOICES_URL: z.string().min(1, 'LAMBDA_SYNC_INVOICES_URL is required'),
  LAMBDA_SYNC_JOURNALS_URL: z.string().min(1, 'LAMBDA_SYNC_JOURNALS_URL is required'),
  LAMBDA_SYNC_MANUAL_JOURNALS_URL: z.string().min(1, 'LAMBDA_SYNC_MANUAL_JOURNALS_URL is required'),
  LAMBDA_SYNC_PAYMENTS_URL: z.string().min(1, 'LAMBDA_SYNC_PAYMENTS_URL is required'),
  LAMBDA_SYNC_TRACKING_CATEGORIES_URL: z.string().min(1, 'LAMBDA_SYNC_TRACKING_CATEGORIES_URL is required'),
  LAMBDA_SYNC_TAX_RATES_URL: z.string().min(1, 'LAMBDA_SYNC_TAX_RATES_URL is required'),
  LAMBDA_SYNC_ATTACHMENTS_URL: z.string().min(1, 'LAMBDA_SYNC_ATTACHMENTS_URL is required'),

  // Profit & Loss
  LAMBDA_SYNC_PL_TRACKING_URL: z.string().min(1, 'LAMBDA_SYNC_PL_TRACKING_URL is required'),

  // Reports
  LAMBDA_BALANCE_SHEET: z.string().min(1, 'LAMBDA_BALANCE_SHEET is required'),
  LAMBDA_SYNC_BS_NO_TRACKING_URL: z.string().min(1, 'LAMBDA_SYNC_BS_NO_TRACKING_URL is required'),
  LAMBDA_SYNC_TRIAL_BALANCE_URL: z.string().min(1, 'LAMBDA_SYNC_TRIAL_BALANCE_URL is required'),

});

// Validate and parse environment variables
const env = envSchema.parse(process.env);

// Export validated configuration
export const config = {
  // Server
  NODE_ENV: env.NODE_ENV,
  PORT: env.PORT,
  HOST: env.HOST,
  IS_PRODUCTION: env.NODE_ENV === 'production',
  IS_DEVELOPMENT: env.NODE_ENV === 'development',
  IS_TEST: env.NODE_ENV === 'test',

  // Database
  DATABASE_URL: env.DATABASE_URL,

  // JWT
  JWT: {
    SECRET: env.JWT_SECRET as string,
    EXPIRES_IN: env.JWT_EXPIRES_IN,
    REFRESH_EXPIRES_IN: env.JWT_REFRESH_EXPIRES_IN,
  },

  // CORS
  CORS: {
    ORIGIN: env.CORS_ORIGIN.split(',').map((origin: string) => origin.trim()),
    CREDENTIALS: env.CORS_CREDENTIALS,
  },

  // Rate Limiting
  RATE_LIMIT: {
    WINDOW_MS: env.RATE_LIMIT_WINDOW_MS,
    MAX_REQUESTS: env.RATE_LIMIT_MAX_REQUESTS,
  },

  // Logging
  LOGGING: {
    LEVEL: env.LOG_LEVEL,
    ERROR_FILE: env.LOG_FILE_ERROR,
    COMBINED_FILE: env.LOG_FILE_COMBINED,
  },

  // Security
  SECURITY: {
    BCRYPT_ROUNDS: env.BCRYPT_ROUNDS,
    SESSION_SECRET: env.SESSION_SECRET,
  },

  // File Upload
  UPLOAD: {
    MAX_FILE_SIZE: env.MAX_FILE_SIZE,
    PATH: env.UPLOAD_PATH,
  },

  // API
  API: {
    VERSION: env.API_VERSION,
    PREFIX: env.API_PREFIX,
  },
  // Xero
  XERO: {
    XERO_CLIENT_ID: env.XERO_CLIENT_ID,
    XERO_CLIENT_SECRET: env.XERO_CLIENT_SECRET,
    XERO_REDIRECT_URI: env.XERO_REDIRECT_URI,
    XERO_AUTH_URL: env.XERO_AUTH_URL,
    XERO_TOKEN_URL: env.XERO_TOKEN_URL,
    REVOKE_URL: env.XERO_REVOKE_URL,
  },
  LAMBDA_ENDPOINT: {
    LAMBDA_SYNC_BANK_TRANSACTIONS_URL: env.LAMBDA_SYNC_BANK_TRANSACTIONS_URL || '',
    LAMBDA_SYNC_BANK_TRANSFERS_URL: env.LAMBDA_SYNC_BANK_TRANSFERS_URL || '',
    LAMBDA_SYNC_CREDIT_NOTES_URL: env.LAMBDA_SYNC_CREDIT_NOTES_URL || '',
    LAMBDA_SYNC_EMPLOYEES_URL: env.LAMBDA_SYNC_EMPLOYEES_URL || '',
    LAMBDA_SYNC_EXPENSE_CLAIMS_URL: env.LAMBDA_SYNC_EXPENSE_CLAIMS_URL || '',
    LAMBDA_SYNC_INVOICES_URL: env.LAMBDA_SYNC_INVOICES_URL || '',
    LAMBDA_SYNC_JOURNALS_URL: env.LAMBDA_SYNC_JOURNALS_URL || '',
    LAMBDA_SYNC_MANUAL_JOURNALS_URL: env.LAMBDA_SYNC_MANUAL_JOURNALS_URL || '',
    LAMBDA_SYNC_PAYMENTS_URL: env.LAMBDA_SYNC_PAYMENTS_URL || '',
    LAMBDA_SYNC_TRACKING_CATEGORIES_URL: env.LAMBDA_SYNC_TRACKING_CATEGORIES_URL || '',
    LAMBDA_SYNC_TAX_RATES_URL: env.LAMBDA_SYNC_TAX_RATES_URL || '',
    LAMBDA_SYNC_ATTACHMENTS_URL: env.LAMBDA_SYNC_ATTACHMENTS_URL || '',
    LAMBDA_SYNC_PL_TRACKING_URL: env.LAMBDA_SYNC_PL_TRACKING_URL || '',
    LAMBDA_BALANCE_SHEET: env.LAMBDA_BALANCE_SHEET || '',
    LAMBDA_SYNC_BS_NO_TRACKING_URL: env.LAMBDA_SYNC_BS_NO_TRACKING_URL || '',
    LAMBDA_SYNC_TRIAL_BALANCE_URL: env.LAMBDA_SYNC_TRIAL_BALANCE_URL || '',
  },
} as const;

// Prisma Client Configuration
const prismaConfig: Prisma.PrismaClientOptions = {
  log: config.IS_DEVELOPMENT ? ['query', 'info', 'warn', 'error'] : ['error'],
  errorFormat: 'pretty',
};

// Prisma singleton with proper configuration
export const prisma = new PrismaClient(prismaConfig);

// Graceful shutdown handler
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

// Legacy exports for backward compatibility
export const PORT = config.PORT;
export const JWT_SECRET = config.JWT.SECRET;
