/**
 * @fileoverview Sync Log Controller
 * @description Controller for managing sync operation logs and monitoring.
 * Provides endpoints for retrieving sync history, viewing detailed logs,
 * and managing retry operations for failed syncs.
 *
 * Key Features:
 * - Sync history retrieval with filtering
 * - Detailed sync log viewing
 * - Manual retry functionality
 * - Real-time sync monitoring
 * - Performance analytics
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-01
 */

import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { SyncStatus } from '@prisma/client';
import {
  getSyncLogs,
  getSyncLogById,
  retrySyncOperation,
  SyncLogFilters,
} from '@services/syncLog.service';
import { successResponse } from '@utils/response';
import { handleError } from '@utils/errorHandler';
import { asyncErrorHandler } from '@middlewares/error.middleware';

/**
 * Validation schema for sync log query parameters
 */
const syncLogQuerySchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  entity: z.string().optional(),
  status: z.nativeEnum(SyncStatus).optional(),
  integration: z.string().optional(),
  limit: z.coerce.number().min(1).max(100).default(50),
  offset: z.coerce.number().min(0).default(0),
});

/**
 * Validation schema for company ID parameter
 */
const companyIdSchema = z.object({
  companyId: z.string().uuid(),
});

/**
 * Validation schema for sync log ID parameter
 */
const syncLogIdSchema = z.object({
  syncLogId: z.string().uuid(),
});

/**
 * Controller: Get sync logs with filtering and pagination
 * Returns paginated list of sync operations for a company
 *
 * Query Parameters:
 * - startDate: Filter logs from this date (ISO string)
 * - endDate: Filter logs until this date (ISO string)
 * - entity: Filter by entity type (Accounts, Invoices, etc.)
 * - status: Filter by sync status (PENDING, SUCCESS, ERROR, etc.)
 * - integration: Filter by integration name (Xero, QuickBooks, etc.)
 * - limit: Number of results per page (1-100, default: 50)
 * - offset: Number of results to skip (default: 0)
 *
 * @example
 * GET /api/v1/companies/{companyId}/sync-logs?entity=Accounts&status=SUCCESS&limit=20
 */
const getSyncLogsHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get user from authenticated request
    const user = req.user as any;
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    // Validate company ID parameter
    const companyValidation = companyIdSchema.safeParse(req.params);
    if (!companyValidation.success) {
      return res.status(400).json({
        success: false,
        error: 'Invalid company ID',
        data: {
          validationErrors: companyValidation.error.errors,
        },
      });
    }

    // Validate query parameters
    const queryValidation = syncLogQuerySchema.safeParse(req.query);
    if (!queryValidation.success) {
      return res.status(400).json({
        success: false,
        error: 'Invalid query parameters',
        data: {
          validationErrors: queryValidation.error.errors,
        },
      });
    }

    const { companyId } = companyValidation.data;
    const filters: SyncLogFilters = {
      ...queryValidation.data,
      startDate: queryValidation.data.startDate
        ? new Date(queryValidation.data.startDate)
        : undefined,
      endDate: queryValidation.data.endDate ? new Date(queryValidation.data.endDate) : undefined,
    };

    // Get sync logs
    const result = await getSyncLogs(user.userId, companyId, filters);

    // Return successful response
    res.status(200).json(
      successResponse('Sync logs retrieved successfully', {
        syncLogs: result.syncLogs,
        pagination: result.pagination,
        appliedFilters: result.filters,
      })
    );
    return;
  } catch (error) {
    next(handleError(error));
  }
};

export const getSyncLogsController = asyncErrorHandler(getSyncLogsHandler);

/**
 * Controller: Get detailed sync log by ID
 * Returns comprehensive details about a specific sync operation
 *
 * @param req.params.companyId - Company UUID
 * @param req.params.syncLogId - Sync log UUID
 * @returns Detailed sync log information
 */
const getSyncLogDetailsHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get user from authenticated request
    const user = req.user as any;
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    // Validate sync log ID parameter
    const validation = syncLogIdSchema.safeParse(req.params);
    if (!validation.success) {
      return res.status(400).json({
        success: false,
        error: 'Invalid sync log ID',
        data: {
          validationErrors: validation.error.errors,
        },
      });
    }

    const { syncLogId } = validation.data;

    // Get sync log details
    const syncLog = await getSyncLogById(user.userId, syncLogId);

    // Return successful response
    res.status(200).json(
      successResponse('Sync log details retrieved successfully', {
        syncLog,
      })
    );
    return;
  } catch (error) {
    next(handleError(error));
  }
};

export const getSyncLogDetailsController = asyncErrorHandler(getSyncLogDetailsHandler);

/**
 * Controller: Retry failed sync operation
 * Manually triggers a retry for a failed sync operation
 *
 * @param req.params.syncLogId - Sync log UUID to retry
 * @returns Retry operation result
 */
const retrySyncHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get user from authenticated request
    const user = req.user as any;
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    // Validate sync log ID parameter
    const validation = syncLogIdSchema.safeParse(req.params);
    if (!validation.success) {
      return res.status(400).json({
        success: false,
        error: 'Invalid sync log ID',
        data: {
          validationErrors: validation.error.errors,
        },
      });
    }

    const { syncLogId } = validation.data;

    // Retry sync operation
    const result = await retrySyncOperation(user.userId, syncLogId);

    // Return successful response
    res.status(200).json(successResponse('Sync retry initiated successfully', result.data));
    return;
  } catch (error) {
    next(handleError(error));
  }
};

export const retrySyncController = asyncErrorHandler(retrySyncHandler);

/**
 * Controller: Get sync statistics for a company
 * Returns aggregated statistics about sync operations
 *
 * @param req.params.companyId - Company UUID
 * @returns Sync statistics and metrics
 */
const getSyncStatsHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get user from authenticated request
    const user = req.user as any;
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    // Validate company ID parameter
    const validation = companyIdSchema.safeParse(req.params);
    if (!validation.success) {
      return res.status(400).json({
        success: false,
        error: 'Invalid company ID',
        data: {
          validationErrors: validation.error.errors,
        },
      });
    }

    const { companyId } = validation.data;

    // Get recent sync logs for statistics
    const result = await getSyncLogs(user.userId, companyId, {
      limit: 100,
      offset: 0,
    });

    // Calculate statistics
    const stats = {
      totalSyncs: result.pagination.totalCount,
      recentSyncs: result.syncLogs.length,
      statusBreakdown: {
        SUCCESS: 0,
        ERROR: 0,
        WARNING: 0,
        PENDING: 0,
        IN_PROGRESS: 0,
        RETRYING: 0,
        CANCELLED: 0,
      },
      entityBreakdown: {} as Record<string, number>,
      averageDuration: 0,
      successRate: 0,
    };

    let totalDurationMs = 0;
    let durationsCount = 0;

    result.syncLogs.forEach((log) => {
      // Status breakdown
      stats.statusBreakdown[log.Status]++;

      // Entity breakdown
      stats.entityBreakdown[log.Entity] = (stats.entityBreakdown[log.Entity] || 0) + 1;

      // Duration calculation
      if (log.Duration) {
        const durationMatch = log.Duration.match(/(\d+\.?\d*)/);
        if (durationMatch && durationMatch[1] !== undefined) {
          totalDurationMs += parseFloat(durationMatch[1]);
          durationsCount++;
        }
      }
    });

    // Calculate averages and rates
    if (durationsCount > 0) {
      stats.averageDuration = totalDurationMs / durationsCount;
    }

    if (result.syncLogs.length > 0) {
      stats.successRate = (stats.statusBreakdown.SUCCESS / result.syncLogs.length) * 100;
    }

    // Return successful response
    res.status(200).json(
      successResponse('Sync statistics retrieved successfully', {
        companyId,
        statistics: stats,
        generatedAt: new Date(),
      })
    );
    return;
  } catch (error) {
    next(handleError(error));
  }
};

export const getSyncStatsController = asyncErrorHandler(getSyncStatsHandler);
