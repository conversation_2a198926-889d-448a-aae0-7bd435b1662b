import { Request, Response, NextFunction } from 'express';
import { registerUser, loginUser, logOutUser, getUserProfile } from '@services/user.service';
import { registerSchema, loginSchema } from '@validators/user.validator';
import { UserResponseDTO } from '@models/dto/user.dto';
import { successResponse } from '@utils/response';
import { handleError } from '@utils/errorHandler';
import { asyncErrorHandler, NotFoundError } from '@middlewares/error.middleware';

/**
 * Controller: Handles user registration flow
 *
 * - Validates request body with Zod
 * - Delegates user creation to the service layer
 * - Maps user to a safe DTO (no password)
 * - Sends standardized JSON response
 */
const registerHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Validate request data using Zod schema
    const validated: any = registerSchema.parse(req.body);

    // Call the business logic service to create user
    const user: any = await registerUser(validated);

    // Build DTO response (hides password)
    const response: UserResponseDTO = {
      id: user.id,
      email: user.email,
      name: user.name,
    };

    res.status(201).json(successResponse('User registered successfully', response));
  } catch (error) {
    next(handleError(error));
  }
};

export const register = asyncErrorHandler(registerHandler);

/**
 * Controller: Handles user login flow
 *
 * - Validates credentials with Zod
 * - Delegates authentication to the service layer
 * - Sends JWT + user DTO in standardized response
 */
const loginHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Validate request data
    const validated = loginSchema.parse(req.body);

    // Call the business logic service to login
    const { user, token, tokenExpiry } = await loginUser(validated);

    // Build DTO with JWT details
    const response: UserResponseDTO = {
      id: user.Id,
      email: user.Email,
      name: user.Name || '',
      token,
      expiresAt: tokenExpiry,
    };

    res.status(200).json(successResponse('Login successful', response));
  } catch (error) {
    next(handleError(error));
  }
};

export const login = asyncErrorHandler(loginHandler);

/**
 * Handles user logout.
 * With JWT, the client is responsible for discarding its token,
 * but you could expand this to a blacklist if needed later.
 */
const logoutHandler = async (req: Request, res: Response, _next: NextFunction) => {
  // Instruct client to remove its token
  await logOutUser(req.user);
  res.status(200).json(
    successResponse('Logout successful', {
      message: 'Please remove your token on the client side.',
    })
  );
};

export const logout = asyncErrorHandler(logoutHandler);

/**
 * Handles user profile retrieval
 */
const getProfileHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.userId;

    const user = await getUserProfile(userId);

    if (!user) {
      throw new NotFoundError('User not found', 'USER_NOT_FOUND');
    }

    res.status(200).json(successResponse('User profile retrieved successfully', user));
  } catch (error) {
    next(handleError(error));
  }
};

export const getProfile = asyncErrorHandler(getProfileHandler);
