/**
 * Utility for sending standardized **success** responses
 *
 * @param message - human-readable success message
 * @param data - the data payload
 * @returns standardized success response object
 */
export const successResponse = <T>(message: string, data: T) => {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString(),
  };
};

/**
 * Utility for sending standardized **error** responses
 *
 * @param error - human-readable error message
 * @param details - optional details payload for debugging or context
 * @returns standardized error response object
 */
export const errorResponse = <DetailsType = Record<string, unknown>>(
  error: string,
  details?: DetailsType
) => {
  return {
    success: false,
    error,
    data: details ?? {},
    timestamp: new Date().toISOString(),
  };
};

/**
 * Utility for sending **paginated** success responses
 *
 * @param message - human-readable success message
 * @param data - array of data results
 * @param pagination - pagination metadata (page, limit, total, totalPages)
 * @returns standardized paginated success response
 */
export const paginatedResponse = <T>(
  message: string,
  data: T[],
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  }
) => {
  return {
    success: true,
    message,
    data,
    pagination,
    timestamp: new Date().toISOString(),
  };
};
