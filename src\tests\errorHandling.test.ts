/**
 * Basic tests for the error handling system
 * Run with: npm test (after setting up a test runner)
 */

import {
  BadRequestError,
  UnauthorizedError,
  ConflictError,
  ValidationError,
  NotFoundError,
  InternalServerError,
} from '../middlewares/error.middleware';
import { handleError, handleZodError, handlePrismaError } from '../utils/errorHandler';
import { ZodError } from 'zod';

describe('Error Handling System', () => {
  describe('Custom Error Classes', () => {
    test('BadRequestError should have correct properties', () => {
      const error = new BadRequestError('Invalid input', 'INVALID_INPUT', { field: 'email' });

      expect(error.statusCode).toBe(400);
      expect(error.isOperational).toBe(true);
      expect(error.message).toBe('Invalid input');
      expect(error.code).toBe('INVALID_INPUT');
      expect(error.details).toEqual({ field: 'email' });
    });

    test('UnauthorizedError should have correct properties', () => {
      const error = new UnauthorizedError('Access denied');

      expect(error.statusCode).toBe(401);
      expect(error.isOperational).toBe(true);
      expect(error.message).toBe('Access denied');
    });

    test('ConflictError should have correct properties', () => {
      const error = new ConflictError('Resource exists', 'DUPLICATE', { id: '123' });

      expect(error.statusCode).toBe(409);
      expect(error.isOperational).toBe(true);
      expect(error.code).toBe('DUPLICATE');
      expect(error.details).toEqual({ id: '123' });
    });

    test('ValidationError should have correct properties', () => {
      const error = new ValidationError('Validation failed', { fields: ['email'] });

      expect(error.statusCode).toBe(422);
      expect(error.isOperational).toBe(true);
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.details).toEqual({ fields: ['email'] });
    });

    test('NotFoundError should have correct properties', () => {
      const error = new NotFoundError('User not found');

      expect(error.statusCode).toBe(404);
      expect(error.isOperational).toBe(true);
      expect(error.message).toBe('User not found');
    });

    test('InternalServerError should have correct properties', () => {
      const error = new InternalServerError('Server error');

      expect(error.statusCode).toBe(500);
      expect(error.isOperational).toBe(false);
      expect(error.message).toBe('Server error');
    });
  });

  describe('Error Conversion', () => {
    test('should convert generic Error to InternalServerError', () => {
      const genericError = new Error('Something went wrong');
      const appError = handleError(genericError);

      expect(appError).toBeInstanceOf(InternalServerError);
      expect(appError.statusCode).toBe(500);
      expect(appError.message).toBe('Something went wrong');
    });

    test('should return AppError as-is', () => {
      const appError = new BadRequestError('Invalid input');
      const result = handleError(appError);

      expect(result).toBe(appError);
    });

    test('should handle ZodError conversion', () => {
      // Mock ZodError structure
      const zodError = {
        name: 'ZodError',
        errors: [
          { path: ['email'], message: 'Invalid email', code: 'invalid_string' },
          { path: ['password'], message: 'Too short', code: 'too_small' },
        ],
      };

      const result = handleZodError(zodError as any);

      expect(result).toBeInstanceOf(ValidationError);
      expect(result.statusCode).toBe(422);
      expect(result.details.errors).toHaveLength(2);
      expect(result.details.errors[0].field).toBe('email');
      expect(result.details.errors[0].message).toBe('Invalid email');
    });

    test('should handle Prisma unique constraint error', () => {
      const prismaError = {
        code: 'P2002',
        meta: { target: ['email'] },
      };

      const result = handlePrismaError(prismaError);

      expect(result).toBeInstanceOf(ConflictError);
      expect(result.statusCode).toBe(409);
      expect(result.code).toBe('UNIQUE_CONSTRAINT_VIOLATION');
      expect(result.message).toContain('email already exists');
    });

    test('should handle Prisma record not found error', () => {
      const prismaError = {
        code: 'P2025',
        meta: { cause: 'Record to delete does not exist.' },
      };

      const result = handlePrismaError(prismaError);

      expect(result).toBeInstanceOf(BadRequestError);
      expect(result.statusCode).toBe(400);
      expect(result.code).toBe('RECORD_NOT_FOUND');
      expect(result.message).toBe('Record not found');
    });
  });

  describe('Error Properties', () => {
    test('should maintain stack trace', () => {
      const error = new BadRequestError('Test error');
      expect(error.stack).toBeDefined();
      expect(error.stack).toContain('BadRequestError');
    });

    test('should have correct name property', () => {
      const error = new ValidationError('Test validation error');
      expect(error.name).toBe('ValidationError');
    });

    test('should handle optional parameters', () => {
      const error1 = new BadRequestError();
      expect(error1.message).toBe('Bad Request');
      expect(error1.statusCode).toBe(400);

      const error2 = new UnauthorizedError('Custom message');
      expect(error2.message).toBe('Custom message');
      expect(error2.code).toBeUndefined();
    });
  });

  describe('Error Details', () => {
    test('should store complex details object', () => {
      const details = {
        userId: '123',
        action: 'delete',
        timestamp: new Date().toISOString(),
        metadata: { source: 'api' },
      };

      const error = new ForbiddenError('Access denied', 'INSUFFICIENT_PERMISSIONS', details);
      expect(error.details).toEqual(details);
    });

    test('should handle null/undefined details', () => {
      const error1 = new BadRequestError('Test', 'CODE', null);
      expect(error1.details).toBeNull();

      const error2 = new BadRequestError('Test', 'CODE', undefined);
      expect(error2.details).toBeUndefined();
    });
  });
});

// Mock test runner functions if not using Jest
if (typeof describe === 'undefined') {
  global.describe = (name: string, fn: Function) => {
    console.log(`\n--- ${name} ---`);
    fn();
  };

  global.test = (name: string, fn: Function) => {
    try {
      fn();
      console.log(`✓ ${name}`);
    } catch (error) {
      console.log(`✗ ${name}: ${error.message}`);
    }
  };

  global.expect = (actual: any) => ({
    toBe: (expected: any) => {
      if (actual !== expected) {
        throw new Error(`Expected ${actual} to be ${expected}`);
      }
    },
    toEqual: (expected: any) => {
      if (JSON.stringify(actual) !== JSON.stringify(expected)) {
        throw new Error(`Expected ${JSON.stringify(actual)} to equal ${JSON.stringify(expected)}`);
      }
    },
    toBeInstanceOf: (expected: any) => {
      if (!(actual instanceof expected)) {
        throw new Error(`Expected ${actual} to be instance of ${expected.name}`);
      }
    },
    toBeDefined: () => {
      if (actual === undefined) {
        throw new Error(`Expected ${actual} to be defined`);
      }
    },
    toContain: (expected: any) => {
      if (!actual.includes(expected)) {
        throw new Error(`Expected ${actual} to contain ${expected}`);
      }
    },
    toHaveLength: (expected: number) => {
      if (actual.length !== expected) {
        throw new Error(`Expected ${actual} to have length ${expected}, got ${actual.length}`);
      }
    },
    toBeNull: () => {
      if (actual !== null) {
        throw new Error(`Expected ${actual} to be null`);
      }
    },
    toBeUndefined: () => {
      if (actual !== undefined) {
        throw new Error(`Expected ${actual} to be undefined`);
      }
    },
  });
}
