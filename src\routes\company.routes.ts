import { Router } from 'express';
import {
  getCompanies,
  getCompanyStats,
  getCompanyByIdController,
  disconnectCompanyXeroController,
  reconnectCompanyXeroController,
} from '@controllers/company.controller';
import { authenticate } from '@middlewares/auth.middleware';

// Initialize a new router instance
const router = Router();

/**
 * Company management routes:
 *
 * GET /companies - Get companies with filtering, pagination, and sorting
 * GET /companies/stats - Get company statistics summary
 * GET /companies/:id - Get a single company by ID
 * POST /companies/:id/disconnect - Disconnect company's Xero connection
 * POST /companies/:id/reconnect - Reconnect company's Xero connection
 */

/**
 * @route GET /companies
 * @desc Get companies with advanced filtering and pagination
 * @access Private (requires authentication)
 *
 * Query Parameters:
 * - name: Filter by company name (partial match, case-insensitive)
 * - connectionStatus: Filter by connection status (ACTIVE, EXPIRED, DISCONNECTED, PENDING)
 * - hasXeroConnection: Filter by Xero connection presence (true/false)
 * - createdAfter: Filter companies created after this date (ISO string)
 * - createdBefore: Filter companies created before this date (ISO string)
 * - limit: Number of results per page (1-100, default: 10)
 * - offset: Number of results to skip (default: 0)
 * - sortBy: Sort field (name, createdAt, updatedAt, default: createdAt)
 * - sortOrder: Sort order (asc, desc, default: desc)
 *
 * @example
 * GET /api/v1/companies?name=acme&connectionStatus=ACTIVE&limit=20&sortBy=name&sortOrder=asc
 *
 * @returns {Object} Response with companies array, pagination info, and applied filters
 */
router.get('/', authenticate, getCompanies);

/**
 * @route GET /companies/stats
 * @desc Get company statistics summary
 * @access Private (requires authentication)
 *
 * @returns {Object} Response with company statistics including:
 * - totalCompanies: Total number of companies
 * - connectionStatusBreakdown: Count by connection status
 * - withXeroConnection: Count of companies with Xero connection
 * - withoutXeroConnection: Count of companies without Xero connection
 * - recentlyCreated: Count of companies created in last 30 days
 */
router.get('/stats', authenticate, getCompanyStats);

/**
 * @route GET /companies/:id
 * @desc Get a single company by ID
 * @access Private (requires authentication)
 *
 * @param {string} id - Company UUID
 *
 * @returns {Object} Response with company details
 */
router.get('/:id', authenticate, getCompanyByIdController);

/**
 * @route delete /companies/:id/disconnect
 * @desc Disconnect company's Xero connection
 * @access Private (requires authentication)
 *
 * @param {string} id - Company UUID
 *
 * @returns {Object} Response with updated company data (disconnected status)
 */
router.delete('/:id/disconnect', authenticate, disconnectCompanyXeroController);

/**
 * @route POST /companies/:id/reconnect
 * @desc Reconnect company's Xero connection by initiating OAuth flow
 * @access Private (requires authentication)
 *
 * @param {string} id - Company UUID
 *
 * @returns {Object} Response with Xero authorization URL for reconnection
 */
router.post('/:id/reconnect', authenticate, reconnectCompanyXeroController);

export default router;
