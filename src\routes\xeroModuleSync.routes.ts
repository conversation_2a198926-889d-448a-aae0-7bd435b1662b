import { Router } from 'express';
import {

  getAvailableModulesController,
} from '@controllers/xeroModuleSync.controller';
import { authenticate } from '@middlewares/auth.middleware';

// Initialize a new router instance
const router = Router();

/**
 * Xero Module Sync routes:
 *
 * - GET /companies/:companyId/xero/modules/sync-status: Get all module sync status for a company
 * - GET /companies/:companyId/xero/modules/:moduleName/sync-status: Get specific module sync status
 * - PUT /companies/:companyId/xero/modules/:moduleName/sync-time: Update module sync time
 * - GET /companies/:companyId/xero/sync-summary: Get sync summary for a company
 * - GET /xero/modules: Get available Xero modules
 */


// Get available Xero modules (no company ID needed)
router.get('/modules/:companyId', authenticate, getAvailableModulesController);

export default router;
