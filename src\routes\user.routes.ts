import { Router } from 'express';
import { getProfile, login, logout, register } from '../controllers/user.controller';
import { authenticate } from '@/middlewares/auth.middleware';

// create a new router instance
const router = Router();

// user registration route
router.post('/register', register);

// user login route
router.post('/login', login);
router.post('/logout', authenticate, logout);
router.get('/me', authenticate, getProfile);

export default router;
