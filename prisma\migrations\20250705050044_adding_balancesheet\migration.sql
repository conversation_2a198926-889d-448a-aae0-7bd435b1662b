-- CreateTable
CREATE TABLE "TrialBalance" (
    "Id" UUID NOT NULL,
    "Year" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "AccountId" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "Amount" DECIMAL(18,2) NOT NULL,
    "CompanyId" UUID NOT NULL,
    "monthEndDebitAmount" DECIMAL(15,2) NOT NULL,
    "monthEndCreditAmount" DECIMAL(15,2) NOT NULL,
    "netChangeAmount" DECIMAL(15,2) NOT NULL,

    CONSTRAINT "TrialBalance_pkey" PRIMARY KEY ("Id")
);

-- AddForeignKey
ALTER TABLE "TrialBalance" ADD CONSTRAINT "TrialBalance_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;
