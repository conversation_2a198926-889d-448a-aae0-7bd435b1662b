/**
 * @fileoverview Sync Logging Service
 * @description Service for managing detailed sync operation logs with retry functionality.
 * Provides comprehensive tracking of API calls, sync operations, and retry mechanisms
 * to support the sync history monitoring UI.
 *
 * Key Features:
 * - Detailed sync operation logging
 * - Retry mechanism with exponential backoff
 * - Sync history retrieval with filtering
 * - Request/response payload tracking
 * - Duration and performance monitoring
 * - Error tracking and analysis
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-01
 */

import { prisma } from '@config/config';
import { SyncStatus } from '@prisma/client';
import logger from '@utils/logger';
import { BadRequestError } from '@middlewares/error.middleware';

/**
 * Interface for creating a new sync log entry
 */
export interface CreateSyncLogRequest {
  requestId?: string;
  entity: string;
  integration: string;
  apiEndpoint?: string;
  method?: string;
  companyId: string;
  userId?: string;
  requestPayload?: any;
  maxRetries?: number;
}

/**
 * Interface for updating sync log status
 */
export interface UpdateSyncLogRequest {
  id: string;
  status: SyncStatus;
  message?: string;
  duration?: string;
  responsePayload?: any;
  errorDetails?: any;
}

/**
 * Interface for sync log filters
 */
export interface SyncLogFilters {
  startDate?: Date | undefined;
  endDate?: Date | undefined;
  entity?: string | undefined;
  status?: SyncStatus | undefined;
  integration?: string | undefined;
  limit?: number | undefined;
  offset?: number | undefined;
}

/**
 * Interface for retry configuration
 */
export interface RetryConfig {
  maxRetries: number;
  baseDelayMs: number;
  maxDelayMs: number;
  backoffMultiplier: number;
}

/**
 * Default retry configuration
 */
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelayMs: 1000, // 1 second
  maxDelayMs: 30000, // 30 seconds
  backoffMultiplier: 2,
};

/**
 * Create a new sync log entry
 * @param request - Sync log creation request
 * @returns Promise with created sync log
 */
export const createSyncLog = async (request: CreateSyncLogRequest) => {
  try {
    const {
      requestId,
      entity,
      integration,
      apiEndpoint,
      method,
      companyId,
      userId,
      requestPayload,
      maxRetries = DEFAULT_RETRY_CONFIG.maxRetries,
    } = request;

    // Verify company exists and user has access
    const company = await prisma.company.findFirst({
      where: {
        Id: companyId,
        ...(userId && { UserId: userId }),
      },
    });

    if (!company) {
      throw new BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
    }

    const syncLog = await prisma.syncLog.create({
      data: {
        RequestId: requestId || `${entity}-${Date.now()}`,
        Entity: entity,
        Integration: integration,
        ApiEndpoint: apiEndpoint ?? null,
        Method: method ?? null,
        Status: SyncStatus.PENDING,
        CompanyId: companyId,
        UserId: userId ?? null,
        RequestPayload: requestPayload,
        MaxRetries: maxRetries,
        RetryCount: 0,
      },
    });

    logger.info('Sync log created successfully', {
      syncLogId: syncLog.Id,
      entity,
      integration,
      companyId,
      userId,
    });

    return syncLog;
  } catch (error: unknown) {
    if (error instanceof BadRequestError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to create sync log', {
      request,
      error: errorMessage,
    });
    throw new BadRequestError(
      `Failed to create sync log: ${errorMessage}`,
      'SYNC_LOG_CREATION_FAILED'
    );
  }
};

/**
 * Update sync log with completion status
 * @param request - Sync log update request
 * @returns Promise with updated sync log
 */
export const updateSyncLog = async (request: UpdateSyncLogRequest) => {
  try {
    const { id, status, message, duration, responsePayload, errorDetails } = request;

    const updateData: any = {
      Status: status,
      Message: message,
      Duration: duration,
      ResponsePayload: responsePayload,
      ErrorDetails: errorDetails,
      UpdatedAt: new Date(),
    };

    // Set completion timestamp for final statuses
    if (['SUCCESS', 'ERROR', 'WARNING', 'CANCELLED'].includes(status)) {
      updateData.CompletedAt = new Date();
    }

    const syncLog = await prisma.syncLog.update({
      where: { Id: id },
      data: updateData,
    });

    logger.info('Sync log updated successfully', {
      syncLogId: id,
      status,
      duration,
    });

    return syncLog;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to update sync log', {
      request,
      error: errorMessage,
    });
    throw new BadRequestError(
      `Failed to update sync log: ${errorMessage}`,
      'SYNC_LOG_UPDATE_FAILED'
    );
  }
};

/**
 * Calculate next retry delay using exponential backoff
 * @param retryCount - Current retry attempt number
 * @param config - Retry configuration
 * @returns Delay in milliseconds
 */
export const calculateRetryDelay = (
  retryCount: number,
  config: RetryConfig = DEFAULT_RETRY_CONFIG
): number => {
  const delay = config.baseDelayMs * Math.pow(config.backoffMultiplier, retryCount);
  return Math.min(delay, config.maxDelayMs);
};

/**
 * Schedule a retry for a failed sync operation
 * @param syncLogId - ID of the sync log to retry
 * @param errorDetails - Details about the failure
 * @returns Promise with updated sync log
 */
export const scheduleRetry = async (syncLogId: string, errorDetails?: any) => {
  try {
    const syncLog = await prisma.syncLog.findUnique({
      where: { Id: syncLogId },
    });

    if (!syncLog) {
      throw new BadRequestError('Sync log not found', 'SYNC_LOG_NOT_FOUND');
    }

    // Check if we've exceeded max retries
    if (syncLog.RetryCount >= syncLog.MaxRetries) {
      return updateSyncLog({
        id: syncLogId,
        status: SyncStatus.ERROR,
        message: `Max retries (${syncLog.MaxRetries}) exceeded`,
        errorDetails,
      });
    }

    // Calculate next retry time
    const retryDelay = calculateRetryDelay(syncLog.RetryCount);
    const nextRetryAt = new Date(Date.now() + retryDelay);

    const updatedSyncLog = await prisma.syncLog.update({
      where: { Id: syncLogId },
      data: {
        Status: SyncStatus.RETRYING,
        RetryCount: syncLog.RetryCount + 1,
        LastRetryAt: new Date(),
        NextRetryAt: nextRetryAt,
        ErrorDetails: errorDetails,
        Message: `Retry ${syncLog.RetryCount + 1}/${syncLog.MaxRetries} scheduled`,
      },
    });

    logger.info('Retry scheduled for sync log', {
      syncLogId,
      retryCount: updatedSyncLog.RetryCount,
      nextRetryAt,
      retryDelay,
    });

    return updatedSyncLog;
  } catch (error: unknown) {
    if (error instanceof BadRequestError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to schedule retry', {
      syncLogId,
      error: errorMessage,
    });
    throw new BadRequestError(`Failed to schedule retry: ${errorMessage}`, 'RETRY_SCHEDULE_FAILED');
  }
};

/**
 * Get sync logs with filtering and pagination
 * @param userId - User ID for authorization
 * @param companyId - Company ID to filter logs
 * @param filters - Additional filters for sync logs
 * @returns Promise with paginated sync logs
 */
export const getSyncLogs = async (
  userId: string,
  companyId: string,
  filters: SyncLogFilters = {}
) => {
  try {
    const { startDate, endDate, entity, status, integration, limit = 50, offset = 0 } = filters;

    // Verify company ownership
    const company = await prisma.company.findFirst({
      where: {
        Id: companyId,
        UserId: userId,
      },
    });

    if (!company) {
      throw new BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
    }

    // Build where clause
    const whereClause: any = {
      CompanyId: companyId,
    };

    if (startDate) {
      whereClause.StartedAt = { gte: startDate };
    }

    if (endDate) {
      whereClause.StartedAt = {
        ...whereClause.StartedAt,
        lte: endDate,
      };
    }

    if (entity) {
      whereClause.Entity = entity;
    }

    if (status) {
      whereClause.Status = status;
    }

    if (integration) {
      whereClause.Integration = integration;
    }

    // Execute queries
    const [syncLogs, totalCount] = await Promise.all([
      prisma.syncLog.findMany({
        where: whereClause,
        orderBy: { StartedAt: 'desc' },
        take: Math.min(limit, 100), // Cap at 100 for performance
        skip: offset,
        include: {
          User: {
            select: {
              Id: true,
              Name: true,
              Email: true,
            },
          },
        },
      }),
      prisma.syncLog.count({
        where: whereClause,
      }),
    ]);

    logger.info('Sync logs retrieved successfully', {
      userId,
      companyId,
      totalCount,
      returnedCount: syncLogs.length,
    });

    return {
      syncLogs,
      pagination: {
        totalCount,
        limit,
        offset,
        hasMore: offset + syncLogs.length < totalCount,
      },
      filters: {
        startDate,
        endDate,
        entity,
        status,
        integration,
      },
    };
  } catch (error: unknown) {
    if (error instanceof BadRequestError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to retrieve sync logs', {
      userId,
      companyId,
      filters,
      error: errorMessage,
    });
    throw new BadRequestError(
      `Failed to retrieve sync logs: ${errorMessage}`,
      'SYNC_LOGS_RETRIEVAL_FAILED'
    );
  }
};

/**
 * Get sync log details by ID
 * @param userId - User ID for authorization
 * @param syncLogId - Sync log ID
 * @returns Promise with sync log details
 */
export const getSyncLogById = async (userId: string, syncLogId: string) => {
  try {
    const syncLog = await prisma.syncLog.findFirst({
      where: {
        Id: syncLogId,
        Company: {
          UserId: userId,
        },
      },
      include: {
        Company: {
          select: {
            Id: true,
            Name: true,
          },
        },
        User: {
          select: {
            Id: true,
            Name: true,
            Email: true,
          },
        },
      },
    });

    if (!syncLog) {
      throw new BadRequestError('Sync log not found or access denied', 'SYNC_LOG_NOT_FOUND');
    }

    logger.info('Sync log details retrieved successfully', {
      userId,
      syncLogId,
    });

    return syncLog;
  } catch (error: unknown) {
    if (error instanceof BadRequestError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to retrieve sync log details', {
      userId,
      syncLogId,
      error: errorMessage,
    });
    throw new BadRequestError(
      `Failed to retrieve sync log: ${errorMessage}`,
      'SYNC_LOG_RETRIEVAL_FAILED'
    );
  }
};

/**
 * Manually retry a failed sync operation
 * @param userId - User ID for authorization
 * @param syncLogId - Sync log ID to retry
 * @returns Promise with retry result
 */
export const retrySyncOperation = async (userId: string, syncLogId: string) => {
  try {
    const syncLog = await prisma.syncLog.findFirst({
      where: {
        Id: syncLogId,
        Company: {
          UserId: userId,
        },
      },
    });

    if (!syncLog) {
      throw new BadRequestError('Sync log not found or access denied', 'SYNC_LOG_NOT_FOUND');
    }

    // Check if retry is allowed
    if (syncLog.Status === SyncStatus.IN_PROGRESS) {
      throw new BadRequestError('Sync operation is already in progress', 'SYNC_IN_PROGRESS');
    }

    if (syncLog.RetryCount >= syncLog.MaxRetries) {
      throw new BadRequestError('Maximum retry attempts exceeded', 'MAX_RETRIES_EXCEEDED');
    }

    // Schedule immediate retry
    const updatedSyncLog = await prisma.syncLog.update({
      where: { Id: syncLogId },
      data: {
        Status: SyncStatus.PENDING,
        RetryCount: syncLog.RetryCount + 1,
        LastRetryAt: new Date(),
        NextRetryAt: null, // Immediate retry
        Message: `Manual retry ${syncLog.RetryCount + 1}/${syncLog.MaxRetries} initiated`,
      },
    });

    logger.info('Manual retry initiated for sync log', {
      userId,
      syncLogId,
      retryCount: updatedSyncLog.RetryCount,
    });

    // TODO: Trigger actual sync operation (SQS message, etc.)
    // await triggerSyncOperation(updatedSyncLog);

    return {
      success: true,
      message: 'Retry initiated successfully',
      data: {
        syncLogId,
        retryCount: updatedSyncLog.RetryCount,
        maxRetries: updatedSyncLog.MaxRetries,
        status: updatedSyncLog.Status,
      },
    };
  } catch (error: unknown) {
    if (error instanceof BadRequestError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to retry sync operation', {
      userId,
      syncLogId,
      error: errorMessage,
    });
    throw new BadRequestError(
      `Failed to retry sync operation: ${errorMessage}`,
      'SYNC_RETRY_FAILED'
    );
  }
};
