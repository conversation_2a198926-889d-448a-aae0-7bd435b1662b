import { z } from 'zod';

/**
 * Enhanced password validation schema with security requirements
 * Ensures passwords meet complexity standards for production security
 */
const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters long')
  .max(128, 'Password must not exceed 128 characters')
  .regex(
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character (@$!%*?&)'
  );

/**
 * Enhanced email validation schema with additional constraints
 * Provides comprehensive email validation for production use
 */
const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .max(254, 'Email must not exceed 254 characters') // RFC 5321 limit
  .email('Please enter a valid email address')
  .regex(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Email format is invalid')
  .transform((email) => email.toLowerCase().trim()); // Normalize email

/**
 * Name validation schema with length and character constraints
 * Ensures names are reasonable and safe for storage
 */
const nameSchema = z
  .string()
  .min(1, 'Name cannot be empty')
  .max(100, 'Name must not exceed 100 characters')
  .regex(/^[a-zA-Z\s'-]+$/, 'Name can only contain letters, spaces, hyphens, and apostrophes')
  .transform((name) => name.trim())
  .optional();

/**
 * Validates user registration input with enhanced security requirements:
 * - name: optional, 1-100 chars, letters/spaces/hyphens/apostrophes only
 * - email: required, valid format, normalized to lowercase
 * - password: required, 8-128 chars, complexity requirements
 *
 * @example
 * ```typescript
 * const validData = registerSchema.parse({
 *   name: "John Doe",
 *   email: "<EMAIL>",
 *   password: "SecurePass123!"
 * });
 * ```
 */
export const registerSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  password: passwordSchema,
});

/**
 * Validates user login input with basic requirements:
 * - email: required, valid format, normalized
 * - password: required, basic length check (no complexity for login)
 *
 * Note: Login validation is less strict than registration since
 * we're validating against existing stored passwords
 *
 * @example
 * ```typescript
 * const validCredentials = loginSchema.parse({
 *   email: "<EMAIL>",
 *   password: "userPassword"
 * });
 * ```
 */
export const loginSchema = z.object({
  email: emailSchema,
  password: z
    .string()
    .min(1, 'Password is required')
    .max(128, 'Password must not exceed 128 characters'),
});

/**
 * Validates user profile update input:
 * - name: optional, same constraints as registration
 * - email: optional, same constraints as registration
 *
 * Used for partial user profile updates where only some fields may be provided
 *
 * @example
 * ```typescript
 * const updateData = updateProfileSchema.parse({
 *   name: "Jane Smith"
 *   // email not provided - will remain unchanged
 * });
 * ```
 */
export const updateProfileSchema = z.object({
  name: nameSchema,
  email: emailSchema.optional(),
});

/**
 * Validates password change input with enhanced security:
 * - currentPassword: required for verification
 * - newPassword: required, full complexity requirements
 *
 * @example
 * ```typescript
 * const passwordChange = changePasswordSchema.parse({
 *   currentPassword: "oldPassword123!",
 *   newPassword: "NewSecurePass456@"
 * });
 * ```
 */
export const changePasswordSchema = z.object({
  currentPassword: z
    .string()
    .min(1, 'Current password is required')
    .max(128, 'Password must not exceed 128 characters'),
  newPassword: passwordSchema,
});

/**
 * Type definitions for validated schemas
 * These provide TypeScript types for the validated data
 */
export type ValidatedRegisterData = z.infer<typeof registerSchema>;
export type ValidatedLoginData = z.infer<typeof loginSchema>;
export type ValidatedUpdateProfileData = z.infer<typeof updateProfileSchema>;
export type ValidatedChangePasswordData = z.infer<typeof changePasswordSchema>;
