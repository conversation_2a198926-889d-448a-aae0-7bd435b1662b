// Test setup file

// Set test environment
process.env['NODE_ENV'] = 'test';
process.env['JWT_SECRET'] = 'test-jwt-secret-key-for-testing-only';
process.env['DATABASE_URL'] = 'postgresql://test:test@localhost:5432/test_db';

// Mock logger in tests to avoid console output
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
}));

// Global test timeout
jest.setTimeout(10000);

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Global test utilities
(global as any).testConfig = {
  validUser: {
    email: '<EMAIL>',
    password: 'password123',
    name: 'Test User',
  },
  invalidUser: {
    email: 'invalid-email',
    password: '123',
  },
  testJwtSecret: 'test-jwt-secret-key-for-testing-only',
};
