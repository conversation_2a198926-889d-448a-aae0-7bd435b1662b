import { z } from 'zod';
import { SYNC_ENTITIES } from '@services/sync.service';

/**
 * Validation schema for sync status query parameters
 */
export const syncStatusQuerySchema = z.object({
  companyId: z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
});

/**
 * Validation schema for sync trigger request
 */
export const syncTriggerSchema = z.object({
  companyId: z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
  entities: z
    .array(z.enum(SYNC_ENTITIES as any))
    .min(1, 'At least one entity must be specified')
    .max(SYNC_ENTITIES.length, `Maximum ${SYNC_ENTITIES.length} entities allowed`)
    .refine(
      (entities) => {
        // Check for duplicates
        const uniqueEntities = new Set(entities);
        return uniqueEntities.size === entities.length;
      },
      {
        message: 'Duplicate entities are not allowed',
      }
    ),
  priority: z.enum(['HIGH', 'NORMAL', 'LOW']).optional().default('NORMAL'),
  fullSync: z.boolean().optional().default(false),
});

/**
 * Validation schema for sync history query parameters
 */
export const syncHistoryQuerySchema = z
  .object({
    companyId: z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
    limit: z
      .string()
      .optional()
      .transform((val) => (val ? parseInt(val, 10) : 50))
      .refine((val) => val >= 1 && val <= 200, {
        message: 'Limit must be between 1 and 200',
      }),
    offset: z
      .string()
      .optional()
      .transform((val) => (val ? parseInt(val, 10) : 0))
      .refine((val) => val >= 0, {
        message: 'Offset must be non-negative',
      }),
    entity: z.enum(SYNC_ENTITIES as any).optional(),
    status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED']).optional(),
    dateFrom: z
      .string()
      .optional()
      .transform((val) => (val ? new Date(val) : undefined))
      .refine(
        (val) => {
          if (val === undefined) return true;
          return !isNaN(val.getTime());
        },
        {
          message: 'dateFrom must be a valid ISO date string',
        }
      ),
    dateTo: z
      .string()
      .optional()
      .transform((val) => (val ? new Date(val) : undefined))
      .refine(
        (val) => {
          if (val === undefined) return true;
          return !isNaN(val.getTime());
        },
        {
          message: 'dateTo must be a valid ISO date string',
        }
      ),
  })
  .refine(
    (data) => {
      // Ensure dateFrom is before dateTo if both are provided
      if (data.dateFrom && data.dateTo) {
        return data.dateFrom <= data.dateTo;
      }
      return true;
    },
    {
      message: 'dateFrom must be before or equal to dateTo',
      path: ['dateFrom'],
    }
  );

/**
 * Validation schema for trigger all sync request
 */
export const triggerAllSyncSchema = z.object({
  companyId: z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
  priority: z.enum(['HIGH', 'NORMAL', 'LOW']).optional().default('NORMAL'),
  fullSync: z.boolean().optional().default(false),
});

/**
 * Validation schema for company ID parameter
 */
export const companyIdParamSchema = z.object({
  id: z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
});

/**
 * Type definitions for validated data
 */
export type SyncStatusQuery = z.infer<typeof syncStatusQuerySchema>;
export type SyncTriggerRequest = z.infer<typeof syncTriggerSchema>;
export type SyncHistoryQuery = z.infer<typeof syncHistoryQuerySchema>;
export type TriggerAllSyncRequest = z.infer<typeof triggerAllSyncSchema>;
export type CompanyIdParam = z.infer<typeof companyIdParamSchema>;

/**
 * Validation schema for sync webhook payload (if needed for Lambda callbacks)
 */
export const syncWebhookSchema = z.object({
  syncId: z.string().uuid('Sync ID must be a valid UUID'),
  companyId: z.string().uuid('Company ID must be a valid UUID'),
  entityType: z.enum(SYNC_ENTITIES as any),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED', 'CANCELLED']),
  recordsProcessed: z.number().int().min(0).optional(),
  recordsSucceeded: z.number().int().min(0).optional(),
  recordsFailed: z.number().int().min(0).optional(),
  errorMessage: z.string().optional(),
  duration: z.number().int().min(0).optional(), // Duration in milliseconds
  metadata: z.record(z.any()).optional(), // Additional metadata from Lambda
});

export type SyncWebhookPayload = z.infer<typeof syncWebhookSchema>;

/**
 * Validation schema for bulk sync operations
 */
export const bulkSyncSchema = z.object({
  companies: z
    .array(
      z.object({
        companyId: z.string().uuid('Company ID must be a valid UUID'),
        entities: z
          .array(z.enum(SYNC_ENTITIES as any))
          .optional()
          .default([...SYNC_ENTITIES]),
      })
    )
    .min(1, 'At least one company must be specified')
    .max(10, 'Maximum 10 companies allowed per bulk operation'),
  priority: z.enum(['HIGH', 'NORMAL', 'LOW']).optional().default('NORMAL'),
  fullSync: z.boolean().optional().default(false),
});

export type BulkSyncRequest = z.infer<typeof bulkSyncSchema>;
