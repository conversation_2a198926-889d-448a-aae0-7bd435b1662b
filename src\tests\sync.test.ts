import request from 'supertest';
import { app } from '@app';
import { prisma } from '@config/database';
import jwt from 'jsonwebtoken';

// Mock data for testing
const mockUser = {
  Id: 'test-user-id',
  Email: '<EMAIL>',
  FirstName: 'Test',
  LastName: 'User',
};

const mockCompany = {
  Id: '01300e5e-819a-47a3-8e0c-995576dcab75',
  UserId: 'test-user-id',
  Name: 'Demo Company (Global)',
  XeroTenantId: 'test-tenant-id',
  ConnectionStatus: 'ACTIVE',
};

const mockToken = jwt.sign(
  { userId: mockUser.Id, email: mockUser.Email },
  process.env.JWT_SECRET || 'test-secret',
  { expiresIn: '1h' }
);

describe('Sync API Endpoints', () => {
  beforeAll(async () => {
    // Setup test data
    await prisma.user.upsert({
      where: { Id: mockUser.Id },
      update: mockUser,
      create: mockUser,
    });

    await prisma.company.upsert({
      where: { Id: mockCompany.Id },
      update: mockCompany,
      create: mockCompany,
    });
  });

  afterAll(async () => {
    // Cleanup test data
    await prisma.syncTrigger.deleteMany({
      where: { CompanyId: mockCompany.Id },
    });
    await prisma.syncLog.deleteMany({
      where: { CompanyId: mockCompany.Id },
    });
    await prisma.company.delete({
      where: { Id: mockCompany.Id },
    });
    await prisma.user.delete({
      where: { Id: mockUser.Id },
    });
    await prisma.$disconnect();
  });

  describe('GET /api/v1/sync/status', () => {
    it('should return sync status for all entities', async () => {
      const response = await request(app)
        .get('/api/v1/sync/status')
        .query({ companyId: mockCompany.Id })
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.companyId).toBe(mockCompany.Id);
      expect(response.body.data.companyName).toBe(mockCompany.Name);
      expect(response.body.data.entities).toBeInstanceOf(Array);
      expect(response.body.data.entities.length).toBeGreaterThan(0);
    });

    it('should return 400 for invalid company ID', async () => {
      const response = await request(app)
        .get('/api/v1/sync/status')
        .query({ companyId: 'invalid-uuid' })
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
    });

    it('should return 401 without authentication', async () => {
      await request(app)
        .get('/api/v1/sync/status')
        .query({ companyId: mockCompany.Id })
        .expect(401);
    });
  });

  describe('POST /api/v1/sync/trigger', () => {
    it('should trigger sync for specific entities', async () => {
      const requestBody = {
        companyId: mockCompany.Id,
        entities: ['Accounts', 'Contacts'],
        priority: 'HIGH',
        fullSync: false,
      };

      const response = await request(app)
        .post('/api/v1/sync/trigger')
        .send(requestBody)
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.companyId).toBe(mockCompany.Id);
      expect(response.body.data.entities).toEqual(['Accounts', 'Contacts']);
      expect(response.body.data.priority).toBe('HIGH');
      expect(response.body.data.triggerIds).toBeInstanceOf(Array);
    });

    it('should return 400 for invalid entities', async () => {
      const requestBody = {
        companyId: mockCompany.Id,
        entities: ['InvalidEntity'],
        priority: 'NORMAL',
        fullSync: false,
      };

      const response = await request(app)
        .post('/api/v1/sync/trigger')
        .send(requestBody)
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(400);

      expect(response.body.success).toBe(false);
    });
  });

  describe('POST /api/v1/sync/trigger-all', () => {
    it('should trigger sync for all entities', async () => {
      const requestBody = {
        companyId: mockCompany.Id,
        priority: 'NORMAL',
        fullSync: true,
      };

      const response = await request(app)
        .post('/api/v1/sync/trigger-all')
        .send(requestBody)
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.companyId).toBe(mockCompany.Id);
      expect(response.body.data.entities).toBeInstanceOf(Array);
      expect(response.body.data.entities.length).toBeGreaterThan(10); // Should include all entities
      expect(response.body.data.fullSync).toBe(true);
    });
  });

  describe('GET /api/v1/sync/history', () => {
    beforeAll(async () => {
      // Create some test sync logs
      await prisma.syncLog.create({
        data: {
          CompanyId: mockCompany.Id,
          EntityType: 'Accounts',
          Status: 'COMPLETED',
          RecordsProcessed: 45,
          RecordsSucceeded: 45,
          RecordsFailed: 0,
          Duration: 12500,
          CreatedAt: new Date('2024-06-12T10:38:30.000Z'),
          CompletedAt: new Date('2024-06-12T10:38:45.000Z'),
        },
      });
    });

    it('should return sync history with pagination', async () => {
      const response = await request(app)
        .get('/api/v1/sync/history')
        .query({
          companyId: mockCompany.Id,
          limit: '10',
          offset: '0',
        })
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.companyId).toBe(mockCompany.Id);
      expect(response.body.data.history).toBeInstanceOf(Array);
      expect(response.body.data.pagination).toBeDefined();
      expect(response.body.data.pagination.totalCount).toBeGreaterThanOrEqual(1);
    });

    it('should filter sync history by entity', async () => {
      const response = await request(app)
        .get('/api/v1/sync/history')
        .query({
          companyId: mockCompany.Id,
          entity: 'Accounts',
          limit: '10',
        })
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.appliedFilters.entity).toBe('Accounts');

      // All returned records should be for Accounts entity
      response.body.data.history.forEach((record: any) => {
        expect(record.EntityType).toBe('Accounts');
      });
    });
  });

  describe('GET /api/v1/sync/entities', () => {
    it('should return list of supported entities', async () => {
      const response = await request(app)
        .get('/api/v1/sync/entities')
        .set('Authorization', `Bearer ${mockToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.entities).toBeInstanceOf(Array);
      expect(response.body.data.totalCount).toBeGreaterThan(10);

      // Check that each entity has required properties
      response.body.data.entities.forEach((entity: any) => {
        expect(entity.name).toBeDefined();
        expect(entity.displayName).toBeDefined();
        expect(entity.description).toBeDefined();
      });

      // Check for specific entities
      const entityNames = response.body.data.entities.map((e: any) => e.name);
      expect(entityNames).toContain('Accounts');
      expect(entityNames).toContain('BankTransactions');
      expect(entityNames).toContain('Contacts');
    });
  });
});
