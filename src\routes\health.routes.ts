import { Router, Request, Response } from 'express';
import { prisma } from '../config/config';
import { successResponse } from '../utils/response';
import { asyncErrorHandler } from '../middlewares/error.middleware';
import logger from '../utils/logger';

const router = Router();

/**
 * Basic health check endpoint
 */
router.get('/', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env['NODE_ENV'] || 'development',
    version: process.env['npm_package_version'] || '1.0.0',
  });
});

/**
 * Detailed health check with database connectivity
 */
router.get(
  '/detailed',
  asyncErrorHandler(async (_req: Request, res: Response) => {
    const startTime = Date.now();

    // Check database connectivity
    let dbStatus = 'OK';
    let dbResponseTime = 0;

    try {
      const dbStart = Date.now();
      await prisma.$queryRaw`SELECT 1`;
      dbResponseTime = Date.now() - dbStart;
    } catch (error) {
      dbStatus = 'ERROR';
      logger.error('Database health check failed', { error: (error as Error).message });
    }

    // Memory usage
    const memoryUsage = process.memoryUsage();

    // System information
    const healthData = {
      status: dbStatus === 'OK' ? 'OK' : 'DEGRADED',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env['NODE_ENV'] || 'development',
      version: process.env['npm_package_version'] || '1.0.0',
      responseTime: Date.now() - startTime,
      services: {
        database: {
          status: dbStatus,
          responseTime: dbResponseTime,
        },
      },
      system: {
        memory: {
          used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          external: Math.round(memoryUsage.external / 1024 / 1024),
          rss: Math.round(memoryUsage.rss / 1024 / 1024),
        },
        cpu: {
          usage: process.cpuUsage(),
        },
      },
    };

    const statusCode = healthData.status === 'OK' ? 200 : 503;
    res.status(statusCode).json(successResponse('Health check completed', healthData));
  })
);

/**
 * Readiness probe for Kubernetes/Docker
 */
router.get(
  '/ready',
  asyncErrorHandler(async (_req: Request, res: Response) => {
    try {
      // Check if the application is ready to serve requests
      await prisma.$queryRaw`SELECT 1`;

      res.status(200).json({
        status: 'READY',
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      logger.error('Readiness check failed', { error: (error as Error).message });
      res.status(503).json({
        status: 'NOT_READY',
        timestamp: new Date().toISOString(),
        error: 'Database connection failed',
      });
    }
  })
);

/**
 * Liveness probe for Kubernetes/Docker
 */
router.get('/live', (_req: Request, res: Response) => {
  res.status(200).json({
    status: 'ALIVE',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

export default router;
