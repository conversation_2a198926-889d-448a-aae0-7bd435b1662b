-- CreateTable
CREATE TABLE "XeroModuleSync" (
    "Id" UUID NOT NULL,
    "CompanyId" UUID NOT NULL,
    "ModuleName" VARCHAR(100) NOT NULL,
    "LastSyncTime" TIMESTAMP(3),
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "XeroModuleSync_pkey" PRIMARY KEY ("Id")
);

-- CreateIndex
CREATE UNIQUE INDEX "XeroModuleSync_CompanyId_ModuleName_key" ON "XeroModuleSync"("CompanyId", "ModuleName");

-- AddForeignKey
ALTER TABLE "XeroModuleSync" ADD CONSTRAINT "XeroModuleSync_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;
