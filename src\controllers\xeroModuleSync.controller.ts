import { Request, Response, NextFunction } from 'express';
import {
  getModuleSyncStatus,
} from '@services/xeroModuleSync.service';
import { successResponse } from '@utils/response';
import { handleError } from '@utils/errorHandler';
import { asyncErrorHandler } from '@middlewares/error.middleware';
import { BadRequestError } from '@middlewares/error.middleware';

// Extend Express Request interface to include 'user'
declare module 'express-serve-static-core' {
  interface Request {
    user?: any;
  }
}


/**
 * Controller: Get available Xero modules
 * GET /xero/modules
 */
const getAvailableModulesHandler = async (_req: Request, res: Response, next: NextFunction) => {
  try {
    const { companyId } = _req.params;
    //  
    if (!companyId) {
      throw new BadRequestError('Company ID is required', 'MISSING_COMPANY_ID');
    }


    const responseData: any[] = await getModuleSyncStatus(companyId);

    res.status(200).json(
      successResponse('Available Xero modules retrieved successfully', responseData)
    );
  } catch (error) {
    next(handleError(error));
  }
};

export const getAvailableModulesController = asyncErrorHandler(getAvailableModulesHandler);
