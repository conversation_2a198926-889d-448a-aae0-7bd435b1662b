/**
 * @fileoverview Xero Sync Service
 * @description Service for syncing data from Xero API with comprehensive logging
 * and retry functionality. Demonstrates integration with the sync logging system.
 *
 * Key Features:
 * - Xero API integration with proper authentication
 * - Automatic sync logging and monitoring
 * - Retry mechanism for failed operations
 * - Performance tracking and analytics
 * - Error handling and reporting
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-01
 */

import axios from 'axios';
import { prisma } from '@config/config';
import { refreshXeroTokens } from '@services/xero.service';
import {
  startSyncOperation,
  completeSyncOperation,
  handleSyncFailure,
  updateCompanySyncDates,
  SyncContext,
} from '@utils/syncLogger';
import { BadRequestError } from '@middlewares/error.middleware';
import logger from '@utils/logger';

/**
 * Interface for Xero Account data
 */
interface XeroAccount {
  AccountID: string;
  Code: string;
  Name: string;
  Type: string;
  BankAccountType?: string;
  Description?: string;
  TaxType?: string;
  EnablePaymentsToAccount?: boolean;
  ShowInExpenseClaims?: boolean;
  Class?: string;
  SystemAccount?: string;
  ReportingCode?: string;
  ReportingCodeName?: string;
  HasAttachments?: boolean;
  UpdatedDateUTC?: string;
}

/**
 * Interface for Xero API response
 */
interface XeroAccountsResponse {
  Accounts: XeroAccount[];
}

/**
 * Sync accounts from Xero API
 * @param userId - User ID for authorization
 * @param companyId - Company ID to sync
 * @param fullSync - Whether to perform full sync or incremental
 * @returns Promise with sync result
 */
export const syncXeroAccounts = async (
  userId: string,
  companyId: string,
  fullSync: boolean = false
): Promise<{ success: boolean; recordsProcessed: number; message: string }> => {
  let context: SyncContext | null = null;

  try {
    // Get company details and verify Xero connection
    const company = await prisma.company.findFirst({
      where: {
        Id: companyId,
        UserId: userId,
      },
    });

    if (!company) {
      throw new BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
    }

    if (!company.XeroTenantId || !company.XeroAccessToken) {
      throw new BadRequestError('Company does not have an active Xero connection', 'NO_XERO_CONNECTION');
    }

    // Start sync operation with logging
    context = await startSyncOperation({
      entity: 'Accounts',
      integration: 'Xero',
      apiEndpoint: 'https://api.xero.com/api.xro/2.0/Accounts',
      method: 'GET',
      companyId,
      userId,
      requestPayload: {
        fullSync,
        tenantId: company.XeroTenantId,
      },
    });

    // Check if token needs refresh
    let accessToken = company.XeroAccessToken;
    if (company.XeroTokenExpiry && new Date() >= company.XeroTokenExpiry) {
      logger.info('Xero token expired, refreshing...', { companyId });
      const refreshedCompany = await refreshXeroTokens(companyId);
      accessToken = refreshedCompany.XeroAccessToken!;
    }

    // Build API request parameters
    const apiUrl = 'https://api.xero.com/api.xro/2.0/Accounts';
    const headers = {
      'Authorization': `Bearer ${accessToken}`,
      'Xero-tenant-id': company.XeroTenantId,
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    };

    // Add incremental sync parameters if not full sync
    let queryParams = '';
    if (!fullSync && company.LastSyncDate) {
      const modifiedSince = company.LastSyncDate.toISOString();
      queryParams = `?ModifiedSince=${encodeURIComponent(modifiedSince)}`;
    }

    // Make API request to Xero
    logger.info('Making Xero API request', {
      syncLogId: context.syncLogId,
      url: `${apiUrl}${queryParams}`,
      fullSync,
    });

    const response = await axios.get<XeroAccountsResponse>(`${apiUrl}${queryParams}`, {
      headers,
      timeout: 30000, // 30 second timeout
    });

    const accounts = response.data.Accounts || [];
    logger.info('Xero API response received', {
      syncLogId: context.syncLogId,
      accountsCount: accounts.length,
      status: response.status,
    });

    // Process and save accounts to database
    let processedCount = 0;
    let errorCount = 0;

    for (const xeroAccount of accounts) {
      try {
        await prisma.account.upsert({
          where: {
            Id: xeroAccount.AccountID,
          },
          update: {
            Code: xeroAccount.Code,
            Name: xeroAccount.Name,
            Type: xeroAccount.Type,
            BankAccountType: xeroAccount.BankAccountType,
            Description: xeroAccount.Description,
            TaxType: xeroAccount.TaxType,
            EnablePaymentsToAccount: xeroAccount.EnablePaymentsToAccount,
            ShowInExpenseClaims: xeroAccount.ShowInExpenseClaims,
            SystemAccount: xeroAccount.Class,
            ReportingCode: xeroAccount.ReportingCode,
            ReportingCodeName: xeroAccount.ReportingCodeName,
            UpdateUtcDate: xeroAccount.UpdatedDateUTC ? new Date(xeroAccount.UpdatedDateUTC) : null,
            CompanyId: companyId,
          },
          create: {
            Id: xeroAccount.AccountID,
            Code: xeroAccount.Code,
            Name: xeroAccount.Name,
            Type: xeroAccount.Type,
            BankAccountType: xeroAccount.BankAccountType,
            Description: xeroAccount.Description,
            TaxType: xeroAccount.TaxType,
            EnablePaymentsToAccount: xeroAccount.EnablePaymentsToAccount,
            ShowInExpenseClaims: xeroAccount.ShowInExpenseClaims,
            SystemAccount: xeroAccount.Class,
            ReportingCode: xeroAccount.ReportingCode,
            ReportingCodeName: xeroAccount.ReportingCodeName,
            UpdateUtcDate: xeroAccount.UpdatedDateUTC ? new Date(xeroAccount.UpdatedDateUTC) : null,
            CompanyId: companyId,
          },
        });
        processedCount++;
      } catch (dbError) {
        errorCount++;
        logger.warn('Failed to save account to database', {
          syncLogId: context.syncLogId,
          accountId: xeroAccount.AccountID,
          error: dbError instanceof Error ? dbError.message : 'Unknown error',
        });
      }
    }

    // Update company sync dates
    await updateCompanySyncDates(companyId, 'Accounts');

    // Complete sync operation
    const hasWarnings = errorCount > 0;
    const message = hasWarnings
      ? `Accounts sync completed with warnings: ${processedCount} processed, ${errorCount} failed`
      : `Accounts sync completed successfully: ${processedCount} accounts processed`;

    await completeSyncOperation(context, {
      success: true,
      data: {
        totalAccounts: accounts.length,
        processedCount,
        errorCount,
        fullSync,
      },
      recordsProcessed: processedCount,
      warning: hasWarnings ? `${errorCount} accounts failed to save` : undefined,
    });

    logger.info('Xero accounts sync completed', {
      syncLogId: context.syncLogId,
      companyId,
      totalAccounts: accounts.length,
      processedCount,
      errorCount,
      fullSync,
    });

    return {
      success: true,
      recordsProcessed: processedCount,
      message,
    };
  } catch (error) {
    const errorObj = error instanceof Error ? error : new Error('Unknown error occurred');

    if (context) {
      // Handle failure with automatic retry scheduling
      await handleSyncFailure(context, errorObj, true);
    }

    logger.error('Xero accounts sync failed', {
      syncLogId: context?.syncLogId,
      companyId,
      userId,
      error: errorObj.message,
      stack: errorObj.stack,
    });

    throw errorObj;
  }
};

/**
 * Sync invoices from Xero API
 * @param userId - User ID for authorization
 * @param companyId - Company ID to sync
 * @param fullSync - Whether to perform full sync or incremental
 * @returns Promise with sync result
 */
export const syncXeroInvoices = async (
  userId: string,
  companyId: string,
  fullSync: boolean = false
): Promise<{ success: boolean; recordsProcessed: number; message: string }> => {
  // Similar implementation to syncXeroAccounts but for invoices
  // This is a placeholder - would implement similar pattern
  throw new Error('Xero invoices sync not yet implemented');
};

/**
 * Sync bank transactions from Xero API
 * @param userId - User ID for authorization
 * @param companyId - Company ID to sync
 * @param fullSync - Whether to perform full sync or incremental
 * @returns Promise with sync result
 */
export const syncXeroBankTransactions = async (
  userId: string,
  companyId: string,
  fullSync: boolean = false
): Promise<{ success: boolean; recordsProcessed: number; message: string }> => {
  // Similar implementation to syncXeroAccounts but for bank transactions
  // This is a placeholder - would implement similar pattern
  throw new Error('Xero bank transactions sync not yet implemented');
};
