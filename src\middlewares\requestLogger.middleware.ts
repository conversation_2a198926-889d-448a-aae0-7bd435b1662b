import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import { config } from '../config/config';

/**
 * Request logging middleware that adds correlation IDs and logs request details
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  // Generate correlation ID if not present
  const correlationId =
    (req.headers['x-correlation-id'] as string) ||
    `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  // Add correlation ID to request and response headers
  req.headers['x-correlation-id'] = correlationId;
  res.setHeader('X-Correlation-ID', correlationId);

  // Store start time for response time calculation
  const startTime = Date.now();

  // Override res.end to log response details
  const originalEnd = res.end;
  res.end = function (chunk?: any, encoding?: any): Response {
    const responseTime = Date.now() - startTime;

    // Log request completion
    const logData = {
      correlationId,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      ip: req.ip || req.socket.remoteAddress,
      userAgent: req.get('User-Agent'),
      userId: (req as any).user?.id || (req as any).user?.userId,
      contentLength: res.get('Content-Length') || '0',
    };

    // Log based on status code
    if (res.statusCode >= 500) {
      logger.error('Request completed with server error', logData);
    } else if (res.statusCode >= 400) {
      // logger.warn('Request completed with client error', logData);
    } else if (config.IS_DEVELOPMENT || responseTime > 1000) {
      // Log slow requests in production, all requests in development
      logger.info('Request completed', logData);
    }

    // Call original end method
    originalEnd.call(this, chunk, encoding);
    return this;
  };

  next();
};
