import { ZodError } from 'zod';
import { JsonWebTokenError, TokenExpiredError } from 'jsonwebtoken';
import {
  ValidationError,
  ConflictError,
  UnauthorizedError,
  BadRequestError,
  InternalServerError,
  AppError,
} from '@middlewares/error.middleware';

/**
 * Utility functions to normalize errors across the app
 * converting different exception types to our AppError hierarchy
 */

/**
 * Handles Zod validation errors
 * Converts Zod field errors into a consistent ValidationError
 */
export const handleZodError = (error: ZodError): ValidationError => {
  const formattedErrors = error.errors.map((err) => ({
    field: err.path.join('.'),
    message: err.message,
    code: err.code,
  }));

  return new ValidationError('Validation failed', {
    errors: formattedErrors,
    totalErrors: error.errors.length,
  });
};

/**
 * Handles Prisma-specific errors
 * Uses error.name and code to classify known errors
 */
export const handlePrismaError = (error: any): AppError => {
  if (error.name === 'PrismaClientKnownRequestError' && 'code' in error) {
    switch (error.code) {
      case 'P2002': {
        // Unique constraint violation
        const fields = (error.meta?.target as string[]) || ['field'];
        return new ConflictError(
          `${fields.join(', ')} already exists`,
          'UNIQUE_CONSTRAINT_VIOLATION',
          { fields, constraint: error.meta?.constraint }
        );
      }

      case 'P2025':
        return new BadRequestError('Record not found', 'RECORD_NOT_FOUND', {
          operation: error.meta?.cause,
        });

      case 'P2003':
        return new BadRequestError(
          'Invalid reference to related record',
          'FOREIGN_KEY_CONSTRAINT',
          { field: error.meta?.field_name }
        );

      case 'P2014':
        return new BadRequestError('Required relation is missing', 'REQUIRED_RELATION_VIOLATION', {
          relation: error.meta?.relation_name,
        });

      default:
        return new InternalServerError('Database operation failed', 'DATABASE_ERROR', {
          code: error.code,
          meta: error.meta,
        });
    }
  }

  // handle other Prisma classes
  switch (error.name) {
    case 'PrismaClientUnknownRequestError':
      return new InternalServerError('Unknown database error occurred', 'DATABASE_UNKNOWN_ERROR');

    case 'PrismaClientRustPanicError':
      return new InternalServerError('Database engine error', 'DATABASE_ENGINE_ERROR');

    case 'PrismaClientInitializationError':
      return new InternalServerError('Database connection failed', 'DATABASE_CONNECTION_ERROR');

    case 'PrismaClientValidationError':
      return new BadRequestError('Invalid database query', 'DATABASE_VALIDATION_ERROR');

    default:
      return new InternalServerError('Unhandled database error', 'DATABASE_ERROR');
  }
};

/**
 * Handles JWT errors and token problems
 */
export const handleJWTError = (error: any): UnauthorizedError => {
  if (error instanceof TokenExpiredError) {
    return new UnauthorizedError('Token has expired', 'TOKEN_EXPIRED', {
      expiredAt: error.expiredAt,
    });
  }

  if (error instanceof JsonWebTokenError) {
    return new UnauthorizedError('Invalid token', 'INVALID_TOKEN', { message: error.message });
  }

  return new UnauthorizedError('Authentication failed', 'AUTH_ERROR');
};

/**
 * Generic error converter that transforms arbitrary errors
 * into our AppError hierarchy for consistent API responses
 */
export const handleError = (error: any): AppError => {
  // Already an AppError? Return directly
  if (error.statusCode && error.isOperational !== undefined) {
    return error as AppError;
  }

  // Zod validation errors
  if (error instanceof ZodError) {
    return handleZodError(error);
  }

  // Prisma errors
  if (error.name?.includes('Prisma') || error.name === 'PrismaClientKnownRequestError') {
    return handlePrismaError(error);
  }

  // JWT errors
  if (error instanceof JsonWebTokenError || error instanceof TokenExpiredError) {
    return handleJWTError(error);
  }

  // Node network errors
  if (error.code === 'ECONNREFUSED') {
    return new InternalServerError('Service unavailable', 'SERVICE_UNAVAILABLE');
  }

  if (error.code === 'ENOTFOUND') {
    return new InternalServerError('External service not found', 'EXTERNAL_SERVICE_ERROR');
  }

  // Default: unhandled
  return new InternalServerError(
    error.message || 'An unexpected error occurred',
    'UNKNOWN_ERROR',
    process.env['NODE_ENV'] === 'development' ? { originalError: error } : undefined
  );
};

/**
 * Wraps async functions to automatically
 * catch errors and forward to next()
 */
export const safeAsync = <T extends any[], R>(fn: (...args: T) => Promise<R>) => {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      throw handleError(error);
    }
  };
};
