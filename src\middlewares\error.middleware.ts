import { NextFunction, Request, Response } from 'express';
import logger from '../utils/logger';
import { errorResponse } from '../utils/response';

/**
 * Custom error interface extending the standard Error
 * Provides additional properties for better error handling
 */
export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
  code?: string;
  details?: any;
}

/**
 * Custom error class for application-specific errors
 */
export class CustomError extends Error implements AppError {
  statusCode: number;
  isOperational: boolean;
  code?: string;
  details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    code?: string,
    details?: any
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    if (code) {
      this.code = code;
    }
    this.details = details;

    // Maintains proper stack trace for where our error was thrown
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Predefined error classes for common HTTP errors
 */
export class BadRequestError extends CustomError {
  constructor(message: string = 'Bad Request', code?: string, details?: any) {
    super(message, 400, true, code, details);
  }
}

export class UnauthorizedError extends CustomError {
  constructor(message: string = 'Unauthorized', code?: string, details?: any) {
    super(message, 401, true, code, details);
  }
}

export class XeroUnauthorizedError extends CustomError {
  constructor(message: string = 'Unauthorized', code?: string, details?: any) {
    super(message, 500, true, code, details);
  }
}

export class ForbiddenError extends CustomError {
  constructor(message: string = 'Forbidden', code?: string, details?: any) {
    super(message, 403, true, code, details);
  }
}

export class NotFoundError extends CustomError {
  constructor(message: string = 'Not Found', code?: string, details?: any) {
    super(message, 404, true, code, details);
  }
}

export class ConflictError extends CustomError {
  constructor(message: string = 'Conflict', code?: string, details?: any) {
    super(message, 409, true, code, details);
  }
}

export class ValidationError extends CustomError {
  constructor(message: string = 'Validation Error', details?: any) {
    super(message, 422, true, 'VALIDATION_ERROR', details);
  }
}

export class InternalServerError extends CustomError {
  constructor(message: string = 'Internal Server Error', code?: string, details?: any) {
    super(message, 500, false, code, details);
  }
}

/**
 * Enhanced centralized error handling middleware
 * Provides comprehensive error logging, classification, and response handling
 */
export const errorHandler = (err: AppError, req: Request, res: Response, _next: NextFunction) => {
  // Set default values for unknown errors
  const statusCode = err.statusCode || 500;
  const isOperational = err.isOperational !== undefined ? err.isOperational : false;
  const isDevelopment = process.env['NODE_ENV'] === 'development';

  // Generate correlation ID for request tracking
  const correlationId =
    req.headers['x-correlation-id'] ||
    `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  // Prepare comprehensive error context for logging
  const errorContext = {
    message: err.message,
    stack: err.stack,
    statusCode,
    isOperational,
    code: err.code,
    details: err.details,
    correlationId,
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip || req.socket.remoteAddress,
      userAgent: req.get('User-Agent'),
      headers: isDevelopment ? req.headers : undefined,
      body: isDevelopment && req.method !== 'GET' ? req.body : undefined,
      params: req.params,
      query: req.query,
      userId: (req as any).user?.userId || (req as any).user?.id,
    },
    timestamp: new Date().toISOString(),
    environment: process.env['NODE_ENV'] || 'development',
  };

  // Log error with appropriate level based on severity
  if (statusCode >= 500) {
    logger.error('Server Error', errorContext);
  } else if (statusCode >= 400) {
    // logger.warn('Client Error', errorContext);
  } else {
    // logger.info('Request Error', errorContext);
  }

  // Determine response message based on environment and error type
  let responseMessage = 'Internal server error';
  let responseDetails = undefined;

  if (isOperational || isDevelopment) {
    responseMessage = err.message;
    responseDetails = err.details;
  }

  // Prepare error response data
  const errorResponseData = {
    correlationId,
    timestamp: new Date().toISOString(),
    path: req.originalUrl,
    method: req.method,
    ...(isDevelopment && {
      stack: err.stack,
      code: err.code,
      isOperational,
    }),
    ...(responseDetails && { details: responseDetails }),
  };

  // Send standardized error response
  res.status(statusCode).json(errorResponse(responseMessage, errorResponseData));
};

/**
 * 404 Not Found handler for unmatched routes
 * Should be placed before the main error handler
 */
export const notFoundHandler = (req: Request, _res: Response, next: NextFunction) => {
  const error = new NotFoundError(
    `Route ${req.method} ${req.originalUrl} not found`,
    'ROUTE_NOT_FOUND'
  );
  next(error);
};

/**
 * Async error wrapper utility
 * Wraps async route handlers to automatically catch and forward errors
 */
export const asyncErrorHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any> | any
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
