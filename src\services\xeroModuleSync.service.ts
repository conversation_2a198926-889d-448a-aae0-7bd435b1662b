import { prisma } from '@config/config';
import logger from '@utils/logger';

/**
 * Xero modules that need to be tracked for sync status
 * These correspond to the different data entities available in Xero API
 */
export const XERO_MODULES = [
  'Accounts',
  'Bank Transactions',
  'Bank Transfers',
  'Budgets',
  'Contacts',
  'Credit Notes',
  'Currencies',
  'Employees',
  'Expense Claims',
  'Invoices',
  'Journals',
  'Manual Journals',
  'Payments',
  'Tracking Categories',
  'Tax Rates',
  'Attachments',
  'Reports (P&L, BS, TB)',
] as const;

export type XeroModuleName = typeof XERO_MODULES[number];

/**
 * Interface for module sync status
 */
export interface ModuleSyncStatus {
  id: string;
  companyId: string;
  moduleName: string;
  lastSyncTime: Date | null;
  createdAt: Date;
  updatedAt: Date;
}



/**
 * Gets module sync status for a company
 * 
 * @param companyId - The company ID to get module sync status for
 * @returns Promise<ModuleSyncStatus[]> - Array of module sync status records
 */
export const getModuleSyncStatus = async (companyId: string): Promise<ModuleSyncStatus[]> => {
  try {
    logger.debug('Fetching module sync status', { companyId });

    const syncRecords = await prisma.xeroModuleSync.findMany({
      where: { CompanyId: companyId },
      orderBy: { ModuleName: 'asc' },
      select: {
        Id: true,
        CompanyId: true,
        ModuleName: true,
        LastSyncTime: true,
        CreatedAt: true,
        UpdatedAt: true,
      }
    });

    return syncRecords.map(record => ({
      id: record.Id,
      companyId: record.CompanyId,
      moduleName: record.ModuleName,
      lastSyncTime: record.LastSyncTime,
      createdAt: record.CreatedAt,
      updatedAt: record.UpdatedAt,
    }));

  } catch (error: any) {
    logger.error('Error fetching module sync status', {
      companyId,
      error: error.message,
      stack: error.stack,
    });
    throw new Error(`Failed to fetch module sync status: ${error.message}`);
  }
};
