-- CreateTable
CREATE TABLE "ProfitLossTracking" (
    "Id" UUID NOT NULL,
    "Year" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "AccountId" TEXT NOT NULL,
    "AccountCode" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "Amount" DECIMAL(65,30) NOT NULL,
    "TrackingCategoryId1" TEXT,
    "TrackingCategoryId2" TEXT,
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ProfitLossTracking_pkey" PRIMARY KEY ("Id")
);
