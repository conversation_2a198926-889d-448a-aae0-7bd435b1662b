2025-07-06 14:10:09 [furgal-backend] [31merror[39m: uncaughtException: router.de is not a function
TypeError: router.de is not a function
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\routes\company.routes.ts:79:8)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "TypeError: router.de is not a function\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts:79:8)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Sun Jul 06 2025 14:10:09 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 28956,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 68558848,
      "heapTotal": 34258944,
      "heapUsed": 15389992,
      "external": 3378696,
      "arrayBuffers": 127359
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 793632.781
  },
  "trace": [
    {
      "column": 8,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts",
      "function": null,
      "line": 79,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-06 14:46:16 [furgal-backend] [31merror[39m: uncaughtException: argument handler must be a function
TypeError: argument handler must be a function
    at Route.<computed> [as post] (D:\Tushar\project\furgal\back\node_modules\router\lib\route.js:228:15)
    at Function.Router.<computed> [as post] (D:\Tushar\project\furgal\back\node_modules\router\index.js:448:19)
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\routes\company.routes.ts:92:8)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13) {
  "error": {},
  "stack": "TypeError: argument handler must be a function\n    at Route.<computed> [as post] (D:\\Tushar\\project\\furgal\\back\\node_modules\\router\\lib\\route.js:228:15)\n    at Function.Router.<computed> [as post] (D:\\Tushar\\project\\furgal\\back\\node_modules\\router\\index.js:448:19)\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts:92:8)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)",
  "exception": true,
  "date": "Sun Jul 06 2025 14:46:16 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 22228,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 68206592,
      "heapTotal": 35045376,
      "heapUsed": 15312312,
      "external": 3323246,
      "arrayBuffers": 71917
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 795799.156
  },
  "trace": [
    {
      "column": 15,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\router\\lib\\route.js",
      "function": "Route.<computed> [as post]",
      "line": 228,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 19,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\router\\index.js",
      "function": "Function.Router.<computed> [as post]",
      "line": 448,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 8,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts",
      "function": null,
      "line": 92,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-06 14:46:24 [furgal-backend] [31merror[39m: uncaughtException: argument handler must be a function
TypeError: argument handler must be a function
    at Route.<computed> [as post] (D:\Tushar\project\furgal\back\node_modules\router\lib\route.js:228:15)
    at Function.Router.<computed> [as post] (D:\Tushar\project\furgal\back\node_modules\router\index.js:448:19)
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\routes\company.routes.ts:92:8)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13) {
  "error": {},
  "stack": "TypeError: argument handler must be a function\n    at Route.<computed> [as post] (D:\\Tushar\\project\\furgal\\back\\node_modules\\router\\lib\\route.js:228:15)\n    at Function.Router.<computed> [as post] (D:\\Tushar\\project\\furgal\\back\\node_modules\\router\\index.js:448:19)\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts:92:8)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)",
  "exception": true,
  "date": "Sun Jul 06 2025 14:46:24 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 31708,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 68558848,
      "heapTotal": 34521088,
      "heapUsed": 15606800,
      "external": 3378696,
      "arrayBuffers": 127367
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 795807.687
  },
  "trace": [
    {
      "column": 15,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\router\\lib\\route.js",
      "function": "Route.<computed> [as post]",
      "line": 228,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 19,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\router\\index.js",
      "function": "Function.Router.<computed> [as post]",
      "line": 448,
      "method": "<computed> [as post]",
      "native": false
    },
    {
      "column": 8,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts",
      "function": null,
      "line": 92,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-06 15:14:50 [furgal-backend] [31merror[39m: uncaughtException: router.DELETE is not a function
TypeError: router.DELETE is not a function
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\routes\company.routes.ts:81:8)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "TypeError: router.DELETE is not a function\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts:81:8)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Sun Jul 06 2025 15:14:50 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 27176,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 69005312,
      "heapTotal": 34258944,
      "heapUsed": 15445112,
      "external": 3378696,
      "arrayBuffers": 127363
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 797513.921
  },
  "trace": [
    {
      "column": 8,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts",
      "function": null,
      "line": 81,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-06 15:14:54 [furgal-backend] [31merror[39m: uncaughtException: router.DELETE is not a function
TypeError: router.DELETE is not a function
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\routes\company.routes.ts:81:8)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "TypeError: router.DELETE is not a function\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts:81:8)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Sun Jul 06 2025 15:14:54 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 28776,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 68612096,
      "heapTotal": 34521088,
      "heapUsed": 15647224,
      "external": 3378696,
      "arrayBuffers": 127363
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 797517.671
  },
  "trace": [
    {
      "column": 8,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts",
      "function": null,
      "line": 81,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-06 15:14:56 [furgal-backend] [31merror[39m: uncaughtException: router.DELETE is not a function
TypeError: router.DELETE is not a function
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\routes\company.routes.ts:81:8)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "TypeError: router.DELETE is not a function\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts:81:8)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Sun Jul 06 2025 15:14:56 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 29964,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 68571136,
      "heapTotal": 35307520,
      "heapUsed": 15336456,
      "external": 3323246,
      "arrayBuffers": 71913
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 797519.484
  },
  "trace": [
    {
      "column": 8,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts",
      "function": null,
      "line": 81,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-06 15:15:01 [furgal-backend] [31merror[39m: uncaughtException: router.de is not a function
TypeError: router.de is not a function
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\routes\company.routes.ts:81:8)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1921439327765635.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "TypeError: router.de is not a function\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts:81:8)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Sun Jul 06 2025 15:15:01 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 25324,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 68243456,
      "heapTotal": 35569664,
      "heapUsed": 15209696,
      "external": 3323246,
      "arrayBuffers": 71909
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 797524.421
  },
  "trace": [
    {
      "column": 8,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\routes\\company.routes.ts",
      "function": null,
      "line": 81,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1921439327765635.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-07 11:42:57 [furgal-backend] [31merror[39m: uncaughtException: Cannot read properties of undefined (reading 'length')
TypeError: Cannot read properties of undefined (reading 'length')
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\validators\sync.validator.ts:19:24)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'length')\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\validators\\sync.validator.ts:19:24)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Mon Jul 07 2025 11:42:57 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 2264,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 67334144,
      "heapTotal": 35045376,
      "heapUsed": 15897200,
      "external": 3333494,
      "arrayBuffers": 84038
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 871200.859
  },
  "trace": [
    {
      "column": 24,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\validators\\sync.validator.ts",
      "function": null,
      "line": 19,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-07 11:43:05 [furgal-backend] [31merror[39m: uncaughtException: Cannot read properties of undefined (reading 'length')
TypeError: Cannot read properties of undefined (reading 'length')
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\validators\sync.validator.ts:19:24)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "TypeError: Cannot read properties of undefined (reading 'length')\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\validators\\sync.validator.ts:19:24)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Mon Jul 07 2025 11:43:05 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 34192,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 68247552,
      "heapTotal": 34521088,
      "heapUsed": 16672016,
      "external": 3388944,
      "arrayBuffers": 139488
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 871208
  },
  "trace": [
    {
      "column": 24,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\validators\\sync.validator.ts",
      "function": null,
      "line": 19,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-07 11:43:42 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(245,59): error TS1128: Declaration or statement expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(245,59): error TS1128: Declaration or statement expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(245,59): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Mon Jul 07 2025 11:43:42 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 35460,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66142208,
      "heapTotal": 34783232,
      "heapUsed": 15305312,
      "external": 3323246,
      "arrayBuffers": 73081
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 871245.171
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-07 12:09:16 [furgal-backend] [31merror[39m: uncaughtException: Cannot find module '@/utils/LambdaTriggerUrl'
Require stack:
- D:\Tushar\project\furgal\back\src\services\sync.service.ts
- D:\Tushar\project\furgal\back\src\controllers\sync.controller.ts
- D:\Tushar\project\furgal\back\src\routes\sync.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
Error: Cannot find module '@/utils/LambdaTriggerUrl'
Require stack:
- D:\Tushar\project\furgal\back\src\services\sync.service.ts
- D:\Tushar\project\furgal\back\src\controllers\sync.controller.ts
- D:\Tushar\project\furgal\back\src\routes\sync.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)
    at Function.Module._resolveFilename (D:\Tushar\project\furgal\back\node_modules\tsconfig-paths\src\register.ts:115:36)
    at Function.Module._load (node:internal/modules/cjs/loader:984:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:24:1)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10) {
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\controllers\\sync.controller.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\sync.routes.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\app.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\server.ts"
    ]
  },
  "stack": "Error: Cannot find module '@/utils/LambdaTriggerUrl'\nRequire stack:\n- D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\controllers\\sync.controller.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\sync.routes.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\app.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)\n    at Function.Module._resolveFilename (D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts:115:36)\n    at Function.Module._load (node:internal/modules/cjs/loader:984:27)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:24:1)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)",
  "exception": true,
  "date": "Mon Jul 07 2025 12:09:16 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 27984,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 63315968,
      "heapTotal": 34258944,
      "heapUsed": 16760376,
      "external": 3400487,
      "arrayBuffers": 150702
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 872779.687
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._resolveFilename",
      "line": 1143,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 36,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts",
      "function": "Module._resolveFilename",
      "line": 115,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 984,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1231,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 1,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 24,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-07 12:09:32 [furgal-backend] [31merror[39m: uncaughtException: Cannot find module '@/utils/LambdaTriggerUrl'
Require stack:
- D:\Tushar\project\furgal\back\src\services\sync.service.ts
- D:\Tushar\project\furgal\back\src\controllers\sync.controller.ts
- D:\Tushar\project\furgal\back\src\routes\sync.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
Error: Cannot find module '@/utils/LambdaTriggerUrl'
Require stack:
- D:\Tushar\project\furgal\back\src\services\sync.service.ts
- D:\Tushar\project\furgal\back\src\controllers\sync.controller.ts
- D:\Tushar\project\furgal\back\src\routes\sync.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)
    at Function.Module._resolveFilename (D:\Tushar\project\furgal\back\node_modules\tsconfig-paths\src\register.ts:115:36)
    at Function.Module._load (node:internal/modules/cjs/loader:984:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:24:1)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-1961404740766468.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10) {
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\controllers\\sync.controller.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\sync.routes.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\app.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\server.ts"
    ]
  },
  "stack": "Error: Cannot find module '@/utils/LambdaTriggerUrl'\nRequire stack:\n- D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\controllers\\sync.controller.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\sync.routes.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\app.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)\n    at Function.Module._resolveFilename (D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts:115:36)\n    at Function.Module._load (node:internal/modules/cjs/loader:984:27)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:24:1)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)",
  "exception": true,
  "date": "Mon Jul 07 2025 12:09:32 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 23664,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 65191936,
      "heapTotal": 34521088,
      "heapUsed": 16557776,
      "external": 3400487,
      "arrayBuffers": 150702
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 872795.062
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._resolveFilename",
      "line": 1143,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 36,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts",
      "function": "Module._resolveFilename",
      "line": 115,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 984,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1231,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 1,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 24,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-1961404740766468.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-07 13:06:13 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(288,2): error TS1472: 'catch' or 'finally' expected.
src/services/sync.service.ts(400,1): error TS1005: '}' expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(288,2): error TS1472: 'catch' or 'finally' expected.
src/services/sync.service.ts(400,1): error TS1005: '}' expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-10910386336107236.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-10910386336107236.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-10910386336107236.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(288,2): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/sync.service.ts(400,1): error TS1005: '}' expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Mon Jul 07 2025 13:06:13 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 19876,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 63959040,
      "heapTotal": 34783232,
      "heapUsed": 15414256,
      "external": 3323246,
      "arrayBuffers": 73144
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 876196.046
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-07 13:06:22 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(289,3): error TS1472: 'catch' or 'finally' expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(289,3): error TS1472: 'catch' or 'finally' expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-10910386336107236.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-10910386336107236.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-10910386336107236.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(289,3): error TS1472: 'catch' or 'finally' expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Mon Jul 07 2025 13:06:22 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 23844,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 63164416,
      "heapTotal": 35045376,
      "heapUsed": 15554040,
      "external": 3323246,
      "arrayBuffers": 73076
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 876205.75
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-07 13:06:23 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(289,3): error TS1472: 'catch' or 'finally' expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(289,3): error TS1472: 'catch' or 'finally' expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-10910386336107236.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-10910386336107236.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-10910386336107236.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(289,3): error TS1472: 'catch' or 'finally' expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Mon Jul 07 2025 13:06:23 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 7968,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 63422464,
      "heapTotal": 35045376,
      "heapUsed": 15578312,
      "external": 3323246,
      "arrayBuffers": 73076
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 876206.843
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-10910386336107236.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 10:16:37 [furgal-backend] [31merror[39m: uncaughtException: Cannot find module '@config/database'
Require stack:
- D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts
- D:\Tushar\project\furgal\back\src\services\xero.service.ts
- D:\Tushar\project\furgal\back\src\controllers\xero.controller.ts
- D:\Tushar\project\furgal\back\src\routes\xero.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
Error: Cannot find module '@config/database'
Require stack:
- D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts
- D:\Tushar\project\furgal\back\src\services\xero.service.ts
- D:\Tushar\project\furgal\back\src\controllers\xero.controller.ts
- D:\Tushar\project\furgal\back\src\routes\xero.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)
    at Function.Module._resolveFilename (D:\Tushar\project\furgal\back\node_modules\tsconfig-paths\src\register.ts:115:36)
    at Function.Module._load (node:internal/modules/cjs/loader:984:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts:1:1)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-6714638864848335.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10) {
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\services\\xero.service.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xero.controller.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\xero.routes.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\app.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\server.ts"
    ]
  },
  "stack": "Error: Cannot find module '@config/database'\nRequire stack:\n- D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\services\\xero.service.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xero.controller.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\xero.routes.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\app.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)\n    at Function.Module._resolveFilename (D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts:115:36)\n    at Function.Module._load (node:internal/modules/cjs/loader:984:27)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6714638864848335.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)",
  "exception": true,
  "date": "Tue Jul 08 2025 10:16:37 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 14008,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 67698688,
      "heapTotal": 34263040,
      "heapUsed": 16654688,
      "external": 3392384,
      "arrayBuffers": 140853
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 952422.312
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._resolveFilename",
      "line": 1143,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 36,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts",
      "function": "Module._resolveFilename",
      "line": 115,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 984,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1231,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 1,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6714638864848335.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 10:17:47 [furgal-backend] [31merror[39m: uncaughtException: Cannot find module '@config/database'
Require stack:
- D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts
- D:\Tushar\project\furgal\back\src\services\xero.service.ts
- D:\Tushar\project\furgal\back\src\controllers\xero.controller.ts
- D:\Tushar\project\furgal\back\src\routes\xero.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
Error: Cannot find module '@config/database'
Require stack:
- D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts
- D:\Tushar\project\furgal\back\src\services\xero.service.ts
- D:\Tushar\project\furgal\back\src\controllers\xero.controller.ts
- D:\Tushar\project\furgal\back\src\routes\xero.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)
    at Function.Module._resolveFilename (D:\Tushar\project\furgal\back\node_modules\tsconfig-paths\src\register.ts:115:36)
    at Function.Module._load (node:internal/modules/cjs/loader:984:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts:1:1)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-6714638864848335.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10) {
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\services\\xero.service.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xero.controller.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\xero.routes.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\app.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\server.ts"
    ]
  },
  "stack": "Error: Cannot find module '@config/database'\nRequire stack:\n- D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\services\\xero.service.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xero.controller.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\xero.routes.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\app.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)\n    at Function.Module._resolveFilename (D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts:115:36)\n    at Function.Module._load (node:internal/modules/cjs/loader:984:27)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6714638864848335.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)",
  "exception": true,
  "date": "Tue Jul 08 2025 10:17:47 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 20960,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 67407872,
      "heapTotal": 35049472,
      "heapUsed": 15286608,
      "external": 3336934,
      "arrayBuffers": 85403
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 952492.515
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._resolveFilename",
      "line": 1143,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 36,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts",
      "function": "Module._resolveFilename",
      "line": 115,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 984,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1231,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 1,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6714638864848335.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 10:17:59 [furgal-backend] [31merror[39m: uncaughtException: Cannot find module '@config/database'
Require stack:
- D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts
- D:\Tushar\project\furgal\back\src\services\xero.service.ts
- D:\Tushar\project\furgal\back\src\controllers\xero.controller.ts
- D:\Tushar\project\furgal\back\src\routes\xero.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
Error: Cannot find module '@config/database'
Require stack:
- D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts
- D:\Tushar\project\furgal\back\src\services\xero.service.ts
- D:\Tushar\project\furgal\back\src\controllers\xero.controller.ts
- D:\Tushar\project\furgal\back\src\routes\xero.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)
    at Function.Module._resolveFilename (D:\Tushar\project\furgal\back\node_modules\tsconfig-paths\src\register.ts:115:36)
    at Function.Module._load (node:internal/modules/cjs/loader:984:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts:1:1)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-6714638864848335.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10) {
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\services\\xero.service.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xero.controller.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\xero.routes.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\app.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\server.ts"
    ]
  },
  "stack": "Error: Cannot find module '@config/database'\nRequire stack:\n- D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\services\\xero.service.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xero.controller.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\xero.routes.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\app.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)\n    at Function.Module._resolveFilename (D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts:115:36)\n    at Function.Module._load (node:internal/modules/cjs/loader:984:27)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6714638864848335.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)",
  "exception": true,
  "date": "Tue Jul 08 2025 10:17:59 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 1280,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 67362816,
      "heapTotal": 34263040,
      "heapUsed": 15520392,
      "external": 3392384,
      "arrayBuffers": 140853
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 952503.812
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._resolveFilename",
      "line": 1143,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 36,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts",
      "function": "Module._resolveFilename",
      "line": 115,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 984,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1231,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 1,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6714638864848335.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 10:18:33 [furgal-backend] [31merror[39m: uncaughtException: Cannot find module '@config/'
Require stack:
- D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts
- D:\Tushar\project\furgal\back\src\services\xero.service.ts
- D:\Tushar\project\furgal\back\src\controllers\xero.controller.ts
- D:\Tushar\project\furgal\back\src\routes\xero.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
Error: Cannot find module '@config/'
Require stack:
- D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts
- D:\Tushar\project\furgal\back\src\services\xero.service.ts
- D:\Tushar\project\furgal\back\src\controllers\xero.controller.ts
- D:\Tushar\project\furgal\back\src\routes\xero.routes.ts
- D:\Tushar\project\furgal\back\src\routes\index.ts
- D:\Tushar\project\furgal\back\src\app.ts
- D:\Tushar\project\furgal\back\src\server.ts
    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)
    at Function.Module._resolveFilename (D:\Tushar\project\furgal\back\node_modules\tsconfig-paths\src\register.ts:115:36)
    at Function.Module._load (node:internal/modules/cjs/loader:984:27)
    at Module.require (node:internal/modules/cjs/loader:1231:19)
    at require (node:internal/modules/helpers:179:18)
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\xeroModuleSync.service.ts:1:1)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-6714638864848335.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10) {
  "error": {
    "code": "MODULE_NOT_FOUND",
    "requireStack": [
      "D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\services\\xero.service.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xero.controller.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\xero.routes.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\app.ts",
      "D:\\Tushar\\project\\furgal\\back\\src\\server.ts"
    ]
  },
  "stack": "Error: Cannot find module '@config/'\nRequire stack:\n- D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\services\\xero.service.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xero.controller.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\xero.routes.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\routes\\index.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\app.ts\n- D:\\Tushar\\project\\furgal\\back\\src\\server.ts\n    at Function.Module._resolveFilename (node:internal/modules/cjs/loader:1143:15)\n    at Function.Module._resolveFilename (D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts:115:36)\n    at Function.Module._load (node:internal/modules/cjs/loader:984:27)\n    at Module.require (node:internal/modules/cjs/loader:1231:19)\n    at require (node:internal/modules/helpers:179:18)\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts:1:1)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6714638864848335.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)",
  "exception": true,
  "date": "Tue Jul 08 2025 10:18:33 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 15364,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 67575808,
      "heapTotal": 34263040,
      "heapUsed": 15527448,
      "external": 3392376,
      "arrayBuffers": 140837
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 952537.843
  },
  "trace": [
    {
      "column": 15,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._resolveFilename",
      "line": 1143,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 36,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\tsconfig-paths\\src\\register.ts",
      "function": "Module._resolveFilename",
      "line": 115,
      "method": "_resolveFilename",
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 984,
      "method": "_load",
      "native": false
    },
    {
      "column": 19,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.require",
      "line": 1231,
      "method": "require",
      "native": false
    },
    {
      "column": 18,
      "file": "node:internal/modules/helpers",
      "function": "require",
      "line": 179,
      "method": null,
      "native": false
    },
    {
      "column": 1,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\xeroModuleSync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-6714638864848335.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 10:56:24 [furgal-backend] [31merror[39m: uncaughtException: Missing parameter name at 15: https://git.new/pathToRegexpError
TypeError: Missing parameter name at 15: https://git.new/pathToRegexpError
    at name (D:\Tushar\project\furgal\back\node_modules\path-to-regexp\src\index.ts:153:13)
    at lexer (D:\Tushar\project\furgal\back\node_modules\path-to-regexp\src\index.ts:168:21)
    at lexer.next (<anonymous>)
    at Iter.peek (D:\Tushar\project\furgal\back\node_modules\path-to-regexp\src\index.ts:188:32)
    at Iter.tryConsume (D:\Tushar\project\furgal\back\node_modules\path-to-regexp\src\index.ts:195:24)
    at Iter.text (D:\Tushar\project\furgal\back\node_modules\path-to-regexp\src\index.ts:213:26)
    at consume (D:\Tushar\project\furgal\back\node_modules\path-to-regexp\src\index.ts:285:23)
    at parse (D:\Tushar\project\furgal\back\node_modules\path-to-regexp\src\index.ts:320:18)
    at D:\Tushar\project\furgal\back\node_modules\path-to-regexp\src\index.ts:503:40
    at Array.map (<anonymous>) {
  "error": {},
  "stack": "TypeError: Missing parameter name at 15: https://git.new/pathToRegexpError\n    at name (D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts:153:13)\n    at lexer (D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts:168:21)\n    at lexer.next (<anonymous>)\n    at Iter.peek (D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts:188:32)\n    at Iter.tryConsume (D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts:195:24)\n    at Iter.text (D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts:213:26)\n    at consume (D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts:285:23)\n    at parse (D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts:320:18)\n    at D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts:503:40\n    at Array.map (<anonymous>)",
  "exception": true,
  "date": "Tue Jul 08 2025 10:56:24 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 2112,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 64212992,
      "heapTotal": 35573760,
      "heapUsed": 19652888,
      "external": 3323462,
      "arrayBuffers": 79205
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 954809.031
  },
  "trace": [
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts",
      "function": "name",
      "line": 153,
      "method": null,
      "native": false
    },
    {
      "column": 21,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts",
      "function": "lexer",
      "line": 168,
      "method": null,
      "native": false
    },
    {
      "column": null,
      "file": null,
      "function": "lexer.next",
      "line": null,
      "method": "next",
      "native": false
    },
    {
      "column": 32,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts",
      "function": "Iter.peek",
      "line": 188,
      "method": "peek",
      "native": false
    },
    {
      "column": 24,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts",
      "function": "Iter.tryConsume",
      "line": 195,
      "method": "tryConsume",
      "native": false
    },
    {
      "column": 26,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts",
      "function": "Iter.text",
      "line": 213,
      "method": "text",
      "native": false
    },
    {
      "column": 23,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts",
      "function": "consume",
      "line": 285,
      "method": null,
      "native": false
    },
    {
      "column": 18,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts",
      "function": "parse",
      "line": 320,
      "method": null,
      "native": false
    },
    {
      "column": 40,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\path-to-regexp\\src\\index.ts",
      "function": null,
      "line": 503,
      "method": null,
      "native": false
    },
    {
      "column": null,
      "file": null,
      "function": "Array.map",
      "line": null,
      "method": "map",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 10:57:47 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/controllers/xeroModuleSync.controller.ts(180,17): error TS1005: ',' expected.
src/controllers/xeroModuleSync.controller.ts(180,36): error TS1005: ',' expected.

Error: ⨯ Unable to compile TypeScript:
src/controllers/xeroModuleSync.controller.ts(180,17): error TS1005: ',' expected.
src/controllers/xeroModuleSync.controller.ts(180,36): error TS1005: ',' expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\controllers\xeroModuleSync.controller.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7172124122313186.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7172124122313186.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7172124122313186.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/controllers/xeroModuleSync.controller.ts(180,17): error TS1005: ',' expected.\r\nsrc/controllers/xeroModuleSync.controller.ts(180,36): error TS1005: ',' expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xeroModuleSync.controller.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 10:57:47 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 34728,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 64913408,
      "heapTotal": 34525184,
      "heapUsed": 18405640,
      "external": 3378912,
      "arrayBuffers": 134788
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 954892.39
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xeroModuleSync.controller.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 10:57:51 [furgal-backend] [31merror[39m: uncaughtException: Identifier 'responseData' has already been declared
D:\Tushar\project\furgal\back\src\controllers\xeroModuleSync.controller.ts:102
        const responseData = await (0, xeroModuleSync_service_1.getModuleSyncStatus)(companyId);
              ^

SyntaxError: Identifier 'responseData' has already been declared
    at internalCompileFunction (node:internal/vm:128:18)
    at wrapSafe (node:internal/modules/cjs/loader:1280:20)
    at Module._compile (node:internal/modules/cjs/loader:1332:27)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7172124122313186.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7172124122313186.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-7172124122313186.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32) {
  "error": {},
  "stack": "D:\\Tushar\\project\\furgal\\back\\src\\controllers\\xeroModuleSync.controller.ts:102\n        const responseData = await (0, xeroModuleSync_service_1.getModuleSyncStatus)(companyId);\n              ^\n\nSyntaxError: Identifier 'responseData' has already been declared\n    at internalCompileFunction (node:internal/vm:128:18)\n    at wrapSafe (node:internal/modules/cjs/loader:1280:20)\n    at Module._compile (node:internal/modules/cjs/loader:1332:27)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)",
  "exception": true,
  "date": "Tue Jul 08 2025 10:57:51 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 33492,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 64471040,
      "heapTotal": 34525184,
      "heapUsed": 18557576,
      "external": 3378912,
      "arrayBuffers": 134645
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 954895.875
  },
  "trace": [
    {
      "column": 18,
      "file": "node:internal/vm",
      "function": "internalCompileFunction",
      "line": 128,
      "method": null,
      "native": false
    },
    {
      "column": 20,
      "file": "node:internal/modules/cjs/loader",
      "function": "wrapSafe",
      "line": 1280,
      "method": null,
      "native": false
    },
    {
      "column": 27,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1332,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-7172124122313186.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:31:19 [furgal-backend] [31merror[39m: uncaughtException: x is not defined
ReferenceError: x is not defined
    at createApp (D:\Tushar\project\furgal\back\src\app.ts:100:9)
    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\app.ts:165:13)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32) {
  "error": {},
  "stack": "ReferenceError: x is not defined\n    at createApp (D:\\Tushar\\project\\furgal\\back\\src\\app.ts:100:9)\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\app.ts:165:13)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:31:19 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 32760,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 67076096,
      "heapTotal": 35049472,
      "heapUsed": 18729184,
      "external": 3331801,
      "arrayBuffers": 87502
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 956904.203
  },
  "trace": [
    {
      "column": 9,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\app.ts",
      "function": "createApp",
      "line": 100,
      "method": null,
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\app.ts",
      "function": null,
      "line": 165,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:49:45 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,7): error TS1109: Expression expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,7): error TS1109: Expression expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(198,7): error TS1109: Expression expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:49:45 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 32004,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66793472,
      "heapTotal": 34000896,
      "heapUsed": 16017032,
      "external": 3378696,
      "arrayBuffers": 128501
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958010.515
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:49:53 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,7): error TS1003: Identifier expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,7): error TS1003: Identifier expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(198,7): error TS1003: Identifier expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:49:53 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 8348,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66985984,
      "heapTotal": 34263040,
      "heapUsed": 16108264,
      "external": 3378696,
      "arrayBuffers": 128501
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958018.421
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:56:01 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,7): error TS1003: Identifier expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,7): error TS1003: Identifier expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(198,7): error TS1003: Identifier expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:56:01 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 16132,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66719744,
      "heapTotal": 34263040,
      "heapUsed": 15915232,
      "external": 3378696,
      "arrayBuffers": 128501
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958386.421
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:56:20 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,7): error TS1003: Identifier expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,7): error TS1003: Identifier expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(198,7): error TS1003: Identifier expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:56:20 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 15232,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66031616,
      "heapTotal": 34525184,
      "heapUsed": 15401528,
      "external": 3323246,
      "arrayBuffers": 73051
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958405.046
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:56:27 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(199,7): error TS1109: Expression expected.
src/services/sync.service.ts(199,9): error TS1005: ',' expected.
src/services/sync.service.ts(200,15): error TS1005: ':' expected.
src/services/sync.service.ts(200,39): error TS1005: ',' expected.
src/services/sync.service.ts(201,15): error TS1005: ':' expected.
src/services/sync.service.ts(201,76): error TS1005: ',' expected.
src/services/sync.service.ts(203,15): error TS1005: ':' expected.
src/services/sync.service.ts(214,11): error TS1005: ',' expected.
src/services/sync.service.ts(216,23): error TS1005: ',' expected.
src/services/sync.service.ts(221,11): error TS1005: ',' expected.
src/services/sync.service.ts(223,5): error TS1005: ',' expected.
src/services/sync.service.ts(235,5): error TS1005: 'try' expected.
src/services/sync.service.ts(239,2): error TS1472: 'catch' or 'finally' expected.
src/services/sync.service.ts(352,1): error TS1005: '}' expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(199,7): error TS1109: Expression expected.
src/services/sync.service.ts(199,9): error TS1005: ',' expected.
src/services/sync.service.ts(200,15): error TS1005: ':' expected.
src/services/sync.service.ts(200,39): error TS1005: ',' expected.
src/services/sync.service.ts(201,15): error TS1005: ':' expected.
src/services/sync.service.ts(201,76): error TS1005: ',' expected.
src/services/sync.service.ts(203,15): error TS1005: ':' expected.
src/services/sync.service.ts(214,11): error TS1005: ',' expected.
src/services/sync.service.ts(216,23): error TS1005: ',' expected.
src/services/sync.service.ts(221,11): error TS1005: ',' expected.
src/services/sync.service.ts(223,5): error TS1005: ',' expected.
src/services/sync.service.ts(235,5): error TS1005: 'try' expected.
src/services/sync.service.ts(239,2): error TS1472: 'catch' or 'finally' expected.
src/services/sync.service.ts(352,1): error TS1005: '}' expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(199,7): error TS1109: Expression expected.\r\nsrc/services/sync.service.ts(199,9): error TS1005: ',' expected.\r\nsrc/services/sync.service.ts(200,15): error TS1005: ':' expected.\r\nsrc/services/sync.service.ts(200,39): error TS1005: ',' expected.\r\nsrc/services/sync.service.ts(201,15): error TS1005: ':' expected.\r\nsrc/services/sync.service.ts(201,76): error TS1005: ',' expected.\r\nsrc/services/sync.service.ts(203,15): error TS1005: ':' expected.\r\nsrc/services/sync.service.ts(214,11): error TS1005: ',' expected.\r\nsrc/services/sync.service.ts(216,23): error TS1005: ',' expected.\r\nsrc/services/sync.service.ts(221,11): error TS1005: ',' expected.\r\nsrc/services/sync.service.ts(223,5): error TS1005: ',' expected.\r\nsrc/services/sync.service.ts(235,5): error TS1005: 'try' expected.\r\nsrc/services/sync.service.ts(239,2): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/sync.service.ts(352,1): error TS1005: '}' expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:56:27 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 7132,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66387968,
      "heapTotal": 35311616,
      "heapUsed": 15902520,
      "external": 3323246,
      "arrayBuffers": 73962
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958412.234
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:56:40 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(199,15): error TS1005: ',' expected.
src/services/sync.service.ts(202,9): error TS1135: Argument expression expected.
src/services/sync.service.ts(202,10): error TS1128: Declaration or statement expected.
src/services/sync.service.ts(203,9): error TS1128: Declaration or statement expected.
src/services/sync.service.ts(229,5): error TS1472: 'catch' or 'finally' expected.
src/services/sync.service.ts(239,5): error TS1005: ',' expected.
src/services/sync.service.ts(243,1): error TS1128: Declaration or statement expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(199,15): error TS1005: ',' expected.
src/services/sync.service.ts(202,9): error TS1135: Argument expression expected.
src/services/sync.service.ts(202,10): error TS1128: Declaration or statement expected.
src/services/sync.service.ts(203,9): error TS1128: Declaration or statement expected.
src/services/sync.service.ts(229,5): error TS1472: 'catch' or 'finally' expected.
src/services/sync.service.ts(239,5): error TS1005: ',' expected.
src/services/sync.service.ts(243,1): error TS1128: Declaration or statement expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(199,15): error TS1005: ',' expected.\r\nsrc/services/sync.service.ts(202,9): error TS1135: Argument expression expected.\r\nsrc/services/sync.service.ts(202,10): error TS1128: Declaration or statement expected.\r\nsrc/services/sync.service.ts(203,9): error TS1128: Declaration or statement expected.\r\nsrc/services/sync.service.ts(229,5): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/sync.service.ts(239,5): error TS1005: ',' expected.\r\nsrc/services/sync.service.ts(243,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:56:40 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 2200,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66949120,
      "heapTotal": 34525184,
      "heapUsed": 16044240,
      "external": 3378696,
      "arrayBuffers": 129000
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958425.312
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:58:50 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,25): error TS1110: Type expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,25): error TS1110: Type expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(198,25): error TS1110: Type expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:58:50 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 27664,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66670592,
      "heapTotal": 34263040,
      "heapUsed": 15928800,
      "external": 3378696,
      "arrayBuffers": 128496
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958555.312
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:59:09 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,43): error TS1003: Identifier expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(198,43): error TS1003: Identifier expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(198,43): error TS1003: Identifier expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:59:09 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 25512,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66711552,
      "heapTotal": 34525184,
      "heapUsed": 15911720,
      "external": 3378696,
      "arrayBuffers": 128502
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958574.515
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:59:22 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(207,5): error TS1472: 'catch' or 'finally' expected.
src/services/sync.service.ts(243,3): error TS1128: Declaration or statement expected.
src/services/sync.service.ts(243,5): error TS1005: 'try' expected.
src/services/sync.service.ts(247,1): error TS1128: Declaration or statement expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(207,5): error TS1472: 'catch' or 'finally' expected.
src/services/sync.service.ts(243,3): error TS1128: Declaration or statement expected.
src/services/sync.service.ts(243,5): error TS1005: 'try' expected.
src/services/sync.service.ts(247,1): error TS1128: Declaration or statement expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(207,5): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/sync.service.ts(243,3): error TS1128: Declaration or statement expected.\r\nsrc/services/sync.service.ts(243,5): error TS1005: 'try' expected.\r\nsrc/services/sync.service.ts(247,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:59:22 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 22544,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66494464,
      "heapTotal": 35049472,
      "heapUsed": 16551080,
      "external": 3323246,
      "arrayBuffers": 73309
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958587.109
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:59:25 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(205,9): error TS1128: Declaration or statement expected.
src/services/sync.service.ts(231,5): error TS1472: 'catch' or 'finally' expected.
src/services/sync.service.ts(241,5): error TS1005: ',' expected.
src/services/sync.service.ts(245,1): error TS1128: Declaration or statement expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(205,9): error TS1128: Declaration or statement expected.
src/services/sync.service.ts(231,5): error TS1472: 'catch' or 'finally' expected.
src/services/sync.service.ts(241,5): error TS1005: ',' expected.
src/services/sync.service.ts(245,1): error TS1128: Declaration or statement expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(205,9): error TS1128: Declaration or statement expected.\r\nsrc/services/sync.service.ts(231,5): error TS1472: 'catch' or 'finally' expected.\r\nsrc/services/sync.service.ts(241,5): error TS1005: ',' expected.\r\nsrc/services/sync.service.ts(245,1): error TS1128: Declaration or statement expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:59:25 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 19644,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66580480,
      "heapTotal": 34263040,
      "heapUsed": 15970896,
      "external": 3378696,
      "arrayBuffers": 128757
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958589.906
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 11:59:33 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(205,7): error TS1005: ':' expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(205,7): error TS1005: ':' expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(205,7): error TS1005: ':' expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 11:59:33 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 20228,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66420736,
      "heapTotal": 35311616,
      "heapUsed": 15432320,
      "external": 3323246,
      "arrayBuffers": 73044
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958598.453
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
2025-07-08 12:00:16 [furgal-backend] [31merror[39m: uncaughtException: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(207,7): error TS1005: ':' expected.

Error: ⨯ Unable to compile TypeScript:
src/services/sync.service.ts(207,7): error TS1005: ':' expected.

    at Object.<anonymous> (D:\Tushar\project\furgal\back\src\services\sync.service.ts:1:7)
    at Module._compile (node:internal/modules/cjs/loader:1369:14)
    at Module._compile (D:\Tushar\project\furgal\back\node_modules\source-map-support\source-map-support.js:568:25)
    at Module.m._compile (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:69:33)
    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)
    at require.extensions..jsx.require.extensions..js (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:114:20)
    at require.extensions.<computed> (C:\Users\<USER>\AppData\Local\Temp\ts-node-dev-hook-2990971567612266.js:71:20)
    at Object.nodeDevHook [as .ts] (D:\Tushar\project\furgal\back\node_modules\ts-node-dev\lib\hook.js:63:13)
    at Module.load (node:internal/modules/cjs/loader:1206:32)
    at Function.Module._load (node:internal/modules/cjs/loader:1022:12) {
  "error": {},
  "stack": "Error: ⨯ Unable to compile TypeScript:\nsrc/services/sync.service.ts(207,7): error TS1005: ':' expected.\r\n\n    at Object.<anonymous> (D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts:1:7)\n    at Module._compile (node:internal/modules/cjs/loader:1369:14)\n    at Module._compile (D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js:568:25)\n    at Module.m._compile (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:69:33)\n    at Module._extensions..js (node:internal/modules/cjs/loader:1427:10)\n    at require.extensions..jsx.require.extensions..js (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:114:20)\n    at require.extensions.<computed> (C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js:71:20)\n    at Object.nodeDevHook [as .ts] (D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js:63:13)\n    at Module.load (node:internal/modules/cjs/loader:1206:32)\n    at Function.Module._load (node:internal/modules/cjs/loader:1022:12)",
  "exception": true,
  "date": "Tue Jul 08 2025 12:00:16 GMT+0530 (India Standard Time)",
  "process": {
    "pid": 25956,
    "uid": null,
    "gid": null,
    "cwd": "D:\\Tushar\\project\\furgal\\back",
    "execPath": "C:\\Program Files\\nodejs\\node.exe",
    "version": "v20.12.2",
    "argv": [
      "C:\\Program Files\\nodejs\\node.exe",
      "src/server.ts"
    ],
    "memoryUsage": {
      "rss": 66347008,
      "heapTotal": 35573760,
      "heapUsed": 15652856,
      "external": 3323246,
      "arrayBuffers": 73044
    }
  },
  "os": {
    "loadavg": [
      0,
      0,
      0
    ],
    "uptime": 958640.812
  },
  "trace": [
    {
      "column": 7,
      "file": "D:\\Tushar\\project\\furgal\\back\\src\\services\\sync.service.ts",
      "function": null,
      "line": 1,
      "method": null,
      "native": false
    },
    {
      "column": 14,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._compile",
      "line": 1369,
      "method": "_compile",
      "native": false
    },
    {
      "column": 25,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\source-map-support\\source-map-support.js",
      "function": "Module._compile",
      "line": 568,
      "method": "_compile",
      "native": false
    },
    {
      "column": 33,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "Module.m._compile",
      "line": 69,
      "method": "_compile",
      "native": false
    },
    {
      "column": 10,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._extensions..js",
      "line": 1427,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions..jsx.require.extensions..js",
      "line": 114,
      "method": ".js",
      "native": false
    },
    {
      "column": 20,
      "file": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ts-node-dev-hook-2990971567612266.js",
      "function": "require.extensions.<computed>",
      "line": 71,
      "method": "<computed>",
      "native": false
    },
    {
      "column": 13,
      "file": "D:\\Tushar\\project\\furgal\\back\\node_modules\\ts-node-dev\\lib\\hook.js",
      "function": "Object.nodeDevHook [as .ts]",
      "line": 63,
      "method": "ts]",
      "native": false
    },
    {
      "column": 32,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module.load",
      "line": 1206,
      "method": "load",
      "native": false
    },
    {
      "column": 12,
      "file": "node:internal/modules/cjs/loader",
      "function": "Module._load",
      "line": 1022,
      "method": "_load",
      "native": false
    }
  ],
  "version": "1.0.0"
}
