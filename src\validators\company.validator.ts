import { z } from 'zod';

/**
 * Validation schema for company listing query parameters
 * Provides comprehensive validation for all filter and pagination options
 */
export const companyListQuerySchema = z.object({
  // Text filters
  name: z
    .string()
    .min(1, 'Name filter cannot be empty')
    .max(100, 'Name filter too long')
    .optional(),

  // Enum filters
  connectionStatus: z
    .enum(['ACTIVE', 'EXPIRED', 'DISCONNECTED', 'PENDING'], {
      errorMap: () => ({
        message: 'Connection status must be ACTIVE, EXPIRED, DISCONNECTED, or PENDING',
      }),
    })
    .optional(),

  // Boolean filters
  hasXeroConnection: z
    .string()
    .optional()
    .transform((val) => {
      if (val === undefined) return undefined;
      if (val === 'true') return true;
      if (val === 'false') return false;
      throw new Error('hasXeroConnection must be "true" or "false"');
    }),

  // Date filters
  createdAfter: z
    .string()
    .optional()
    .transform((val) => {
      if (!val) return undefined;
      const date = new Date(val);
      if (isNaN(date.getTime())) {
        throw new Error('createdAfter must be a valid ISO date string');
      }
      return date;
    }),

  createdBefore: z
    .string()
    .optional()
    .transform((val) => {
      if (!val) return undefined;
      const date = new Date(val);
      if (isNaN(date.getTime())) {
        throw new Error('createdBefore must be a valid ISO date string');
      }
      return date;
    }),

  // Pagination
  limit: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 10))
    .refine((val) => !isNaN(val) && val >= 1 && val <= 100, {
      message: 'Limit must be a number between 1 and 100',
    }),

  offset: z
    .string()
    .optional()
    .transform((val) => (val ? parseInt(val, 10) : 0))
    .refine((val) => !isNaN(val) && val >= 0, {
      message: 'Offset must be a non-negative number',
    }),

  // Sorting
  sortBy: z
    .enum(['name', 'createdAt', 'updatedAt'], {
      errorMap: () => ({ message: 'sortBy must be name, createdAt, or updatedAt' }),
    })
    .optional()
    .default('createdAt'),

  sortOrder: z
    .enum(['asc', 'desc'], {
      errorMap: () => ({ message: 'sortOrder must be asc or desc' }),
    })
    .optional()
    .default('desc'),
});

/**
 * Type definition for validated company list query parameters
 */
export type CompanyListQuery = z.infer<typeof companyListQuerySchema>;

/**
 * Validation schema for company creation
 */
export const createCompanySchema = z.object({
  name: z
    .string()
    .min(1, 'Company name is required')
    .max(150, 'Company name must be 150 characters or less')
    .trim(),

  financialYearEnd: z
    .string()
    .max(20, 'Financial year end must be 20 characters or less')
    .optional(),
});

/**
 * Validation schema for company updates
 */
export const updateCompanySchema = z.object({
  name: z
    .string()
    .min(1, 'Company name cannot be empty')
    .max(150, 'Company name must be 150 characters or less')
    .trim()
    .optional(),

  financialYearEnd: z
    .string()
    .max(20, 'Financial year end must be 20 characters or less')
    .optional(),
});

/**
 * Validation schema for company ID parameter
 */
export const companyIdSchema = z.object({
  id: z.string().uuid('Company ID must be a valid UUID'),
});

/**
 * Type definitions for validated schemas
 */
export type CreateCompanyData = z.infer<typeof createCompanySchema>;
export type UpdateCompanyData = z.infer<typeof updateCompanySchema>;
export type CompanyIdParams = z.infer<typeof companyIdSchema>;
