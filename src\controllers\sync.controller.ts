/**
 * @fileoverview Sync Controller
 * @description Controller layer for entity synchronization operations.
 *
 * <AUTHOR> Furgal Development Team
 */

import { Request, Response, NextFunction } from 'express';
import {
  getSyncStatus,
  triggerEntitySync,
  triggerAllEntitiesSync,
  getSyncHistory,
  SYNC_ENTITIES,
  SyncEntity,
} from '@services/sync.service';
import { successResponse } from '@utils/response';
import { handleError } from '@utils/errorHandler';
import { asyncErrorHandler } from '@middlewares/error.middleware';
import {
  syncStatusQuerySchema,
  syncTriggerSchema,
  syncHistoryQuerySchema,
} from '@validators/sync.validator';

/**
 * Get sync status for all supported Xero entities
 */
const getSyncStatusHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const user = req.user as { userId?: string };
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    const validated = syncStatusQuerySchema.safeParse(req.query);
    if (!validated.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid query parameters',
        data: validated.error.format(),
      });
      return;
    }

    const { companyId } = validated.data;

    const status = await getSyncStatus(user.userId, companyId);

    res.status(200).json(successResponse('Sync status retrieved successfully', status));
  } catch (error) {
    next(handleError(error));
  }
};

export const getSyncStatusController = asyncErrorHandler(getSyncStatusHandler);

/**
 * Trigger sync for specific entities
 */
const triggerSyncHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const user = req.user as { userId?: string };
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    const validated = syncTriggerSchema.safeParse(req.body);
    if (!validated.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid request data',
        data: validated.error.format(),
      });
      return;
    }

    const result = await triggerEntitySync(user.userId, validated.data);

    res.status(200).json(successResponse('Sync triggered successfully', result.data));
  } catch (error) {
    next(handleError(error));
  }
};

export const triggerSyncController = asyncErrorHandler(triggerSyncHandler);

/**
 * Trigger sync for ALL entities
 */
const triggerAllSyncHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const user = req.user as { userId?: string };
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    const { companyId, priority = 'NORMAL', fullSync = false } = req.body;

    if (!companyId || typeof companyId !== 'string') {
      res.status(400).json({
        success: false,
        error: 'Company ID is required',
      });
      return;
    }

    const result = await triggerAllEntitiesSync(user.userId, companyId, {
      priority,
      fullSync,
    });

    res.status(200).json(successResponse('Full sync triggered successfully', result.data));
  } catch (error) {
    next(handleError(error));
  }
};

export const triggerAllSyncController = asyncErrorHandler(triggerAllSyncHandler);

/**
 * Get paginated sync history
 */
const getSyncHistoryHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const user = req.user as { userId?: string };
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    const validated = syncHistoryQuerySchema.safeParse(req.query);

    if (!validated.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid query parameters',
        data: validated.error.format(),
      });
      return;
    }

    const { companyId, ...options } = validated.data;

    const history = await getSyncHistory(user.userId, companyId, {
      limit: options.limit,
      offset: options.offset,
      entity: options.entity,
      ...(options.status !== undefined ? { status: options.status } : {}),
      ...(options.dateFrom !== undefined ? { dateFrom: options.dateFrom } : {}),
      ...(options.dateTo !== undefined ? { dateTo: options.dateTo } : {}),
    });

    res.status(200).json(successResponse('Sync history retrieved successfully', history));
  } catch (error) {
    next(handleError(error));
  }
};

export const getSyncHistoryController = asyncErrorHandler(getSyncHistoryHandler);

/**
 * Get the supported entities
 */
const getSupportedEntitiesHandler = async (
  _req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const entities = SYNC_ENTITIES.map((entity) => ({
      name: entity,
      displayName: entity.replace(/([A-Z])/g, ' $1').trim(),
      description: getEntityDescription(entity),
    }));

    res.status(200).json(
      successResponse('Supported entities retrieved successfully', {
        entities,
        totalCount: entities.length,
      })
    );
  } catch (error) {
    next(handleError(error));
  }
};

export const getSupportedEntitiesController = asyncErrorHandler(getSupportedEntitiesHandler);

/**
 * Helper to describe each entity
 */
function getEntityDescription(entity: SyncEntity): string {
  const descriptions: Record<SyncEntity, string> = {
    Accounts: 'Chart of accounts and balances',
    BankTransactions: 'Bank transaction records',
    BankTransfers: 'Bank transfer entries',
    Budgets: 'Financial budgets',
    Contacts: 'Customers and suppliers',
    CreditNotes: 'Issued credit notes',
    Currencies: 'Currency exchange rates',
    Employees: 'Employee details',
    ExpenseClaims: 'Employee expense claims',
    Invoices: 'Customer and supplier invoices',
    Journals: 'Journal entries',
    ManualJournals: 'Manual journal adjustments',
    Items: 'Product/service catalog',
    Payments: 'Payment transactions',
    PurchaseOrders: 'Purchase orders',
    TaxRates: 'Tax rates and codes',
    TrackingCategories: 'Tracking categories',
    Attachments: 'File and document attachments',
    TrialBalance: 'TrialBalance',
    ProfitLoss: 'ProfitLoss',
    BalanceSheet: 'BalanceSheet'
  };
  return descriptions[entity] || 'Xero entity';
}
