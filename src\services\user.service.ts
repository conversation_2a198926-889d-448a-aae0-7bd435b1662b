import bcrypt from 'bcryptjs';
import {
  createUser,
  findUserByEmail,
  findUserById,
  logoutUser,
  updateUser,
} from '@repositories/user.repository';
import { UserRegisterDTO, UserLoginDTO, UserCreateData, UserEntity } from '@models/dto/user.dto';
import { generateToken } from '@utils/token';
import { ConflictError, UnauthorizedError } from '@middlewares/error.middleware';
import { safeAsync } from '@utils/errorHandler';
import { config } from '@config/config';
import logger from '@utils/logger';

/**
 * Service: Handles user registration
 *
 * Validates user doesn't exist, hashes password securely,
 * creates user in database with proper error handling
 *
 * @param input - User registration data from client
 * @returns Promise resolving to created user entity
 * @throws {ConflictError} If user already exists with email
 * @throws {Error} If password hashing or user creation fails
 *
 * @example
 * ```typescript
 * const newUser = await registerUser({
 *   email: '<EMAIL>',
 *   password: 'securePassword123',
 *   name: '<PERSON>'
 * });
 * ```
 */
const registerUserHandler = async (input: UserRegisterDTO): Promise<UserEntity> => {
  logger.info('Starting user registration process', { email: input.email });

  // Check for existing user by email to prevent duplicates
  const existingUser = await findUserByEmail(input.email);
  if (existingUser) {
    logger.warn('Registration attempt with existing email', { email: input.email });
    throw new ConflictError(
      'A user already exists with this email address.',
      'USER_ALREADY_EXISTS',
      { email: input.email }
    );
  }

  // Hash password using configured bcrypt rounds for security
  logger.debug('Hashing password for new user', { email: input.email });
  const hashedPassword = await bcrypt.hash(input.password, config.SECURITY.BCRYPT_ROUNDS);

  // Prepare user creation data with proper typing
  const userData: UserCreateData = {
    Email: input.email,
    Password: hashedPassword,
    ...(input.name && { Name: input.name }),
    IsActive: true,
    IsVerified: false, // Users start unverified
  };

  // Create user in database
  const user = await createUser(userData);

  logger.info('User registration completed successfully', {
    userId: user.Id,
    email: user.Email,
  });

  return user;
};

export const registerUser = safeAsync(registerUserHandler);

/**
 * Service: Handles user login authentication
 *
 * Validates credentials, generates JWT token, updates last login,
 * and returns user data with authentication token
 *
 * @param input - User login credentials from client
 * @returns Promise resolving to user, token, and expiry data
 * @throws {UnauthorizedError} If credentials are invalid or user not found
 * @throws {Error} If token generation or user update fails
 *
 * @example
 * ```typescript
 * const loginResult = await loginUser({
 *   email: '<EMAIL>',
 *   password: 'userPassword123'
 * });
 * console.log('Login successful:', loginResult.user.Email);
 * ```
 */
const loginUserHandler = async (
  input: UserLoginDTO
): Promise<{
  user: UserEntity;
  token: string;
  tokenExpiry: Date;
}> => {
  logger.info('Starting user login process', { email: input.email });

  // Find user by email
  const user = await findUserByEmail(input.email);
  if (!user) {
    logger.warn('Login attempt with non-existent email', { email: input.email });
    throw new UnauthorizedError('Invalid email or password.', 'INVALID_CREDENTIALS', {
      email: input.email,
    });
  }

  // Check if user account is active
  if (!user.IsActive) {
    logger.warn('Login attempt with inactive account', {
      userId: user.Id,
      email: user.Email,
    });
    throw new UnauthorizedError(
      'Account is deactivated. Please contact support.',
      'ACCOUNT_DEACTIVATED',
      { userId: user.Id }
    );
  }

  // Compare plaintext password with hashed password
  const isPasswordValid = await bcrypt.compare(input.password, user.Password);
  if (!isPasswordValid) {
    logger.warn('Login attempt with invalid password', {
      userId: user.Id,
      email: user.Email,
    });
    throw new UnauthorizedError('Invalid email or password.', 'INVALID_CREDENTIALS', {
      email: input.email,
    });
  }

  // Generate JWT token with expiry from config
  const tokenExpiryMs = convertExpiryToMills(config.JWT.EXPIRES_IN);
  const token = generateToken(
    {
      userId: user.Id,
      email: user.Email,
      isVerified: user.IsVerified,
    },
    tokenExpiryMs
  );

  // Calculate token expiry date
  const tokenExpiry = new Date(Date.now() + tokenExpiryMs);

  // Update user's last login timestamp
  await updateUser(user.Id, {
    LastLoginAt: new Date(),
  });

  logger.info('User login completed successfully', {
    userId: user.Id,
    email: user.Email,
  });

  return { user, token, tokenExpiry };
};

export const loginUser = safeAsync(loginUserHandler);

/**
 * Utility: converts string expiry (e.g., "1h", "7d") to milliseconds
 * so we can set a real JS Date for token expiry
 */
const convertExpiryToMills = (expiresIn: string | number): number => {
  if (typeof expiresIn === 'number') return expiresIn * 1000;

  const match: any = expiresIn.match(/^(\d+)([smhd])$/);
  if (!match) return 3600000; // default 1h

  const value = parseInt(match[1], 10);
  const unit = match[2];

  switch (unit) {
    case 's':
      return value * 1000;
    case 'm':
      return value * 60 * 1000;
    case 'h':
      return value * 60 * 60 * 1000;
    case 'd':
      return value * 24 * 60 * 60 * 1000;
    default:
      return 3600000;
  }
};

const logOutUserHandler = async (input: any) => {
  await logoutUser(input.userId);
  return {
    message: 'User logged out successfully',
  };
};
export const logOutUser = safeAsync(logOutUserHandler);

/**
 * Retrieves the authenticated user's profile by user ID
 * @param userId - user ID
 * @returns user object
 */
const getUserProfileHandler = async (userId: string) => {
  return findUserById(userId);
};
export const getUserProfile = safeAsync(getUserProfileHandler);
