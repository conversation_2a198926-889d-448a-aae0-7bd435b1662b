import { Request, Response, NextFunction } from 'express';
import {
  getXeroAuthUrl,
  handleXeroCallback,
  disconnectXero,
  refreshXeroTokens,
} from '@services/xero.service';

// Extend Express Request interface to include 'user'
declare module 'express-serve-static-core' {
  interface Request {
    user?: any;
  }
}
import { successResponse } from '@utils/response';
import { handleError } from '@utils/errorHandler';
import { asyncErrorHandler } from '@middlewares/error.middleware';

/**
 * Controller: Generates Xero OAuth2 authorization URL
 */
const generateAuthUrlHandler = async (_req: Request, res: Response, next: NextFunction) => {
  try {
    // service generates a secure OAuth2 URL
    const url = getXeroAuthUrl();
    res.status(200).json(successResponse('Xero authorization URL generated', { url }));
  } catch (error) {
    next(handleError(error));
  }
};

export const generateAuthUrl = asyncErrorHandler(generateAuthUrlHandler);

/**
 * Controller: Handles Xero OAuth2 callback
 */
const xeroCallbackHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // get authorization code from query params
    const code = req.query['code'] as string;
    const companyId = req.query['state'] as string;

    if (!code || !companyId) {
      throw new Error('Missing required OAuth2 parameters.');
    }
    const user: any = req.user;

    // delegate token exchange to the service
    const updatedCompany = await handleXeroCallback(code, user.userId);

    res.status(200).json(successResponse('Xero authorization successful', updatedCompany));
  } catch (error) {
    next(handleError(error));
  }
};

export const xeroCallback = asyncErrorHandler(xeroCallbackHandler);

/**
 * Controller: Disconnects Xero from a company
 */
const disconnectHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { companyId } = req.body;

    if (!companyId) {
      throw new Error('companyId is required in the request body.');
    }

    await disconnectXero(companyId);

    res.status(200).json(successResponse('Xero account disconnected successfully', null));
  } catch (error) {
    next(handleError(error));
  }
};

export const xeroDisconnect = asyncErrorHandler(disconnectHandler);

/**
 * Controller: Refreshes Xero tokens for a company
 */
const refreshXeroTokenHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { companyId } = req.body;

    if (!companyId) {
      throw new Error('Missing companyId in request body.');
    }

    const updatedCompany = await refreshXeroTokens(companyId);

    res.status(200).json(successResponse('Xero tokens refreshed successfully', updatedCompany));
  } catch (error) {
    next(handleError(error));
  }
};

export const refreshXeroToken = asyncErrorHandler(refreshXeroTokenHandler);
