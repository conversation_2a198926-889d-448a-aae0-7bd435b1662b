import { z } from 'zod';

/**
 * Enhanced UUID validation schema with proper error messages
 * Ensures UUIDs are properly formatted and not empty
 */
const uuidSchema = z
  .string()
  .min(1, 'ID cannot be empty')
  .uuid('Must be a valid UUID format')
  .transform((id) => id.trim());

/**
 * Enhanced authorization code validation schema
 * Validates OAuth2 authorization codes with proper constraints
 */
const authCodeSchema = z
  .string()
  .min(1, 'Authorization code is required')
  .max(2048, 'Authorization code is too long') // Reasonable limit for OAuth codes
  .regex(/^[A-Za-z0-9_-]+$/, 'Authorization code contains invalid characters')
  .transform((code) => code.trim());

/**
 * Validates the OAuth2 callback query parameters with enhanced security:
 * - code: required, valid OAuth2 authorization code format
 * - state: required, valid UUID format (used as companyId)
 *
 * This schema ensures the OAuth2 callback contains valid parameters
 * and prevents injection attacks through malformed input
 *
 * @example
 * ```typescript
 * const callbackData = xeroCallbackQuerySchema.parse({
 *   code: "abc123def456",
 *   state: "550e8400-e29b-41d4-a716-************"
 * });
 * ```
 */
export const xeroCallbackQuerySchema = z.object({
  code: authCodeSchema,
  state: uuidSchema,
});

/**
 * Validates company ID input with enhanced validation:
 * - companyId: required, valid UUID format
 *
 * Used for disconnect, refresh, and other company-specific operations
 *
 * @example
 * ```typescript
 * const companyData = companyIdSchema.parse({
 *   companyId: "550e8400-e29b-41d4-a716-************"
 * });
 * ```
 */
export const companyIdSchema = z.object({
  companyId: uuidSchema,
});

/**
 * Validates company creation/update input:
 * - name: required, reasonable length limits
 * - financialYearEnd: optional, valid date format
 *
 * @example
 * ```typescript
 * const companyInfo = companyInfoSchema.parse({
 *   name: "Acme Corporation",
 *   financialYearEnd: "2024-03-31"
 * });
 * ```
 */
export const companyInfoSchema = z.object({
  name: z
    .string()
    .min(1, 'Company name is required')
    .max(150, 'Company name must not exceed 150 characters')
    .regex(/^[a-zA-Z0-9\s&.,'-]+$/, 'Company name contains invalid characters')
    .transform((name) => name.trim()),
  financialYearEnd: z
    .string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Financial year end must be in YYYY-MM-DD format')
    .optional(),
});

/**
 * Type definitions for validated schemas
 * These provide TypeScript types for the validated data
 */
export type ValidatedXeroCallbackQuery = z.infer<typeof xeroCallbackQuerySchema>;
export type ValidatedCompanyId = z.infer<typeof companyIdSchema>;
export type ValidatedCompanyInfo = z.infer<typeof companyInfoSchema>;
