/* eslint-disable prettier/prettier */
import { z } from "zod";

/**
 * List of allowed entities
 */
export const allowedEntities = [
    "Bank Transactions",
    "Bank Transfers",
    "Credit Notes",
    "Employees",
    "Expense Claims",
    "Invoices",
    "Journals",
    "Manual Journals",
    "Payments",
    "Tracking Categories",
    "Tax Rates",
    "Attachments",
    "Profit and Loss with Tracking",
    "Profit and Loss without Tracking",
    "(P&L, Balance Sheet, Trial Balance)",
    "Balance Sheet without Tracking",
    "Trial Balance",
] as const;

export type AllowedEntity = typeof allowedEntities[number];

export const syncEntitySchema = z.enum(allowedEntities);
