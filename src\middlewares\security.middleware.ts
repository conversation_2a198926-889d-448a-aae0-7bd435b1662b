import { Request, Response, NextFunction } from 'express';
import { config } from '../config/config';
import { BadRequestError } from './error.middleware';

/**
 * Security middleware for additional security measures
 */
export const securityMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Remove sensitive headers
  res.removeHeader('X-Powered-By');

  // Add security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');

  if (config.IS_PRODUCTION) {
    res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
  }

  // Validate Content-Type for POST/PUT/PATCH requests
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    const contentType = req.get('Content-Type');
    if (
      contentType &&
      !contentType.includes('application/json') &&
      !contentType.includes('multipart/form-data')
    ) {
      return next(
        new BadRequestError(
          'Invalid Content-Type. Expected application/json or multipart/form-data',
          'INVALID_CONTENT_TYPE'
        )
      );
    }
  }

  // Validate request size
  const contentLength = parseInt(req.get('Content-Length') || '0');
  if (contentLength > config.UPLOAD.MAX_FILE_SIZE) {
    return next(
      new BadRequestError(
        `Request too large. Maximum size is ${config.UPLOAD.MAX_FILE_SIZE} bytes`,
        'REQUEST_TOO_LARGE'
      )
    );
  }

  next();
};
