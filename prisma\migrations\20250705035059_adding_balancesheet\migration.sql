-- CreateTable
CREATE TABLE "BalanceSheetTracking" (
    "Id" UUID NOT NULL,
    "Year" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "AccountId" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "Amount" DECIMAL(18,2) NOT NULL,
    "TrackingCategoryId1" TEXT,
    "TrackingCategoryId2" TEXT,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "BalanceSheetTracking_pkey" PRIMARY KEY ("Id")
);

-- CreateTable
CREATE TABLE "BalanceSheet" (
    "Id" UUID NOT NULL,
    "Year" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "AccountId" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "Amount" DECIMAL(18,2) NOT NULL,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "BalanceSheet_pkey" PRIMARY KEY ("Id")
);

-- AddForeignKey
ALTER TABLE "BalanceSheetTracking" ADD CONSTRAINT "BalanceSheetTracking_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "BalanceSheet" ADD CONSTRAINT "BalanceSheet_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;
