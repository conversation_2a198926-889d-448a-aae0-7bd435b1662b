import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create admin user

  // Create test users
  const testUsers = [
    {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: await bcrypt.hash('password123', 12),
    },
    {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: await bcrypt.hash('password123', 12),
    },
    {
      email: '<EMAIL>',
      name: '<PERSON>',
      password: await bcrypt.hash('password123', 12),
    },
  ];

  for (const userData of testUsers) {
    await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: {
        ...userData,
        isActive: true,
        isVerified: true,
      },
    });
  }

  console.log('✅ Database seeding completed successfully!');
  console.log(`Created ${testUsers.length} test users`);
}

main()
  .catch((e) => {
    console.error('❌ Error during database seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
