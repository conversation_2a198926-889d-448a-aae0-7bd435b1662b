/**
 * @fileoverview
 * Utility function to log an API call to ApiLog.
 */

import { prisma } from "@config/config";

export interface LogApiCallInput {
    companyId: string;
    userId?: string;
    method: string;
    apiUrl: string;
    status: string;
    integrationName: string;
    apiName: string;
    requestPayload?: any;
    responsePayload?: any;
    duration?: string;
}

export const logApiCall = async ({
    companyId,
    userId,
    method,
    apiUrl,
    status,
    integrationName,
    apiName,
    requestPayload,
    responsePayload,
    duration,
}: LogApiCallInput) => {
    await prisma.apiLog.create({
        data: {
            CompanyId: companyId,
            UserId: userId ?? null,
            Method: method,
            ApiUrl: apiUrl,
            Status: status,
            IntegrationName: integrationName,
            ApiName: apiName,
            ApiRequest: requestPayload,
            ApiResponse: responsePayload,
            Duration: duration ?? null,
        },
    });
};
