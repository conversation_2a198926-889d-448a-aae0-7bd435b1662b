/**
 * @fileoverview
 * Centralized Axios client with reusable configuration
 * for consistent Lambda and API calls.
 */

import axios from "axios";

const axiosClient = axios.create({
    timeout: 15000, // 15 second timeout
    headers: {
        "Content-Type": "application/json",
    },
});

// Interceptors for logging or modifying requests
axiosClient.interceptors.request.use((config) => {
    // you could add correlation ID headers here if needed
    return config;
});

// Interceptors for consistent error handling
axiosClient.interceptors.response.use(
    (response) => response,
    (error) => {
        // you can enrich the error if you want
        return Promise.reject(error);
    }
);

export default axiosClient;
