import { Router } from 'express';
import {
  getSyncStatus<PERSON><PERSON>roller,
  triggerSync<PERSON><PERSON>roller,
  triggerAllSyncController,
  getSyncHistoryController,
  getSupportedEntitiesController,
} from '@controllers/sync.controller';
import { authenticate } from '@middlewares/auth.middleware';

// Initialize a new router instance
const router = Router();

/**
 * Entity Synchronization routes:
 *
 * GET /sync/status - Get sync status for all entities
 * POST /sync/trigger - Trigger sync for specific entities
 * POST /sync/trigger-all - Trigger sync for all entities
 * GET /sync/history - Get sync history with filtering
 * GET /sync/entities - Get supported entities list
 */

/**
 * @route GET /sync/status
 * @desc Get synchronization status for all entities
 * @access Private (requires authentication)
 *
 * Query Parameters:
 * - companyId: Company UUID (required)
 *
 * @returns {Object} Response with sync status for all entities
 */
router.get('/status', authenticate, getSyncStatusController);

/**
 * @route POST /sync/trigger
 * @desc Trigger synchronization for specific entities
 * @access Private (requires authentication)
 *
 * Request Body:
 * - companyId: Company UUID (required)
 * - entities: Array of entity types to sync (required)
 * - priority: Sync priority - HIGH, NORMAL, LOW (optional, default: NORMAL)
 * - fullSync: Whether to perform full sync (optional, default: false)
 *
 * @returns {Object} Response with trigger confirmation and details
 */
router.post('/trigger', authenticate, triggerSyncController);

/**
 * @route POST /sync/trigger-all
 * @desc Trigger synchronization for all supported entities
 * @access Private (requires authentication)
 *
 * Request Body:
 * - companyId: Company UUID (required)
 * - priority: Sync priority - HIGH, NORMAL, LOW (optional, default: NORMAL)
 * - fullSync: Whether to perform full sync (optional, default: false)
 *
 * @returns {Object} Response with trigger confirmation for all entities
 */
router.post('/trigger-all', authenticate, triggerAllSyncController);

/**
 * @route GET /sync/history
 * @desc Get synchronization history with filtering and pagination
 * @access Private (requires authentication)
 *
 * Query Parameters:
 * - companyId: Company UUID (required)
 * - limit: Results per page (optional, default: 50, max: 200)
 * - offset: Results to skip (optional, default: 0)
 * - entity: Filter by entity type (optional)
 * - status: Filter by sync status (optional)
 * - dateFrom: Filter from date (ISO string, optional)
 * - dateTo: Filter to date (ISO string, optional)
 *
 * @returns {Object} Response with paginated sync history
 */
router.get('/history', authenticate, getSyncHistoryController);

/**
 * @route GET /sync/entities
 * @desc Get list of supported entities for synchronization
 * @access Private (requires authentication)
 *
 * @returns {Object} Response with supported entities list and descriptions
 */
router.get('/entities', authenticate, getSupportedEntitiesController);

export default router;
