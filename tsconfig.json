{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "rootDir": "./src", "outDir": "./dist", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@config/*": ["config/*"], "@controllers/*": ["controllers/*"], "@middlewares/*": ["middlewares/*"], "@models/*": ["models/*"], "@repositories/*": ["repositories/*"], "@routes/*": ["routes/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@validators/*": ["validators/*"], "@types/*": ["types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}