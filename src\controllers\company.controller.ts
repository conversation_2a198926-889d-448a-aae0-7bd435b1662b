/**
 * @fileoverview Company Controller
 * @description Controller layer for company management operations.
 * Handles HTTP requests for company listing, retrieval, and Xero connection management.
 *
 * Key Features:
 * - Get companies with advanced filtering and pagination
 * - Retrieve individual company details
 * - Disconnect Xero connections
 * - User authorization and company ownership validation
 * - Comprehensive input validation
 * - Structured error handling
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-01
 */

import { Request, Response, NextFunction } from 'express';
import {
  getCompaniesWithFilters,
  CompanyListFilters,
  getCompanyById,
  disconnectCompanyXero,
  reconnectCompanyXero,
} from '@services/xero.service';
import { successResponse } from '@utils/response';
import { handleError } from '@utils/errorHandler';
import { asyncErrorHandler } from '@middlewares/error.middleware';
import { companyListQuerySchema, companyIdSchema } from '@validators/company.validator';

// Extend Express Request interface to include authenticated user
declare module 'express-serve-static-core' {
  interface Request {
    user?: any;
  }
}

/**
 * Controller: Get companies with filtering, pagination, and sorting
 *
 * Query Parameters:
 * - name: Filter by company name (partial match, case-insensitive)
 * - connectionStatus: Filter by connection status (ACTIVE, EXPIRED, DISCONNECTED, PENDING)
 * - hasXeroConnection: Filter by Xero connection presence (true/false)
 * - createdAfter: Filter companies created after this date (ISO string)
 * - createdBefore: Filter companies created before this date (ISO string)
 * - limit: Number of results per page (1-100, default: 10)
 * - offset: Number of results to skip (default: 0)
 * - sortBy: Sort field (name, createdAt, updatedAt, default: createdAt)
 * - sortOrder: Sort order (asc, desc, default: desc)
 *
 * @example
 * GET /api/v1/companies?name=acme&connectionStatus=ACTIVE&limit=20&sortBy=name&sortOrder=asc
 */
const getCompaniesHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get user from authenticated request
    const user = req.user as { userId: string; email?: string } | undefined;
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    // Validate and parse query parameters
    const validationResult = companyListQuerySchema.safeParse(req.query);

    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid query parameters',
        data: {
          validationErrors: validationResult.error.errors,
        },
      });
      return;
    }

    // The validated data matches CompanyListFilters interface
    const filters = validationResult.data as CompanyListFilters;

    // Get companies with filters
    const result = await getCompaniesWithFilters(user.userId, filters);

    // Return successful response
    res.status(200).json(
      successResponse('Companies retrieved successfully', {
        companies: result.companies,
        pagination: result.pagination,
        appliedFilters: result.filters,
      })
    );
    return;
  } catch (error) {
    next(handleError(error));
  }
};

export const getCompanies = asyncErrorHandler(getCompaniesHandler);

/**
 * Controller: Get company statistics
 * Returns summary statistics about user's companies
 */
const getCompanyStatsHandler = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Get user from authenticated request
    const user = req.user as { userId: string; email?: string } | undefined;
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    // Get all companies for statistics
    const allCompanies = await getCompaniesWithFilters(user.userId, { limit: 1000 });

    const stats = {
      totalCompanies: allCompanies.pagination.totalCount,
      connectionStatusBreakdown: {
        ACTIVE: 0,
        EXPIRED: 0,
        DISCONNECTED: 0,
        PENDING: 0,
      },
      withXeroConnection: 0,
      withoutXeroConnection: 0,
      recentlyCreated: 0, // Created in last 30 days
    };

    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    // Calculate statistics
    allCompanies.companies.forEach((company) => {
      // Connection status breakdown
      stats.connectionStatusBreakdown[company.ConnectionStatus]++;

      // Xero connection status
      if (company.XeroTenantId) {
        stats.withXeroConnection++;
      } else {
        stats.withoutXeroConnection++;
      }

      // Recently created
      if (company.CreatedAt > thirtyDaysAgo) {
        stats.recentlyCreated++;
      }
    });

    res.status(200).json(successResponse('Company statistics retrieved successfully', stats));
  } catch (error) {
    next(handleError(error));
  }
};

export const getCompanyStats = asyncErrorHandler(getCompanyStatsHandler);

/**
 * Controller: Get a single company by ID
 * Returns detailed information about a specific company owned by the authenticated user
 *
 * @param req.params.id - Company UUID
 * @returns Company details or 404 if not found/not owned by user
 */
const getCompanyByIdHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get user from authenticated request
    const user = req.user as { userId: string; email?: string } | undefined;
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    // Validate company ID parameter
    const validationResult = companyIdSchema.safeParse(req.params);

    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid company ID',
        data: {
          validationErrors: validationResult.error.errors,
        },
      });
      return;
    }

    const { id: companyId } = validationResult.data;

    // Get company by ID
    const company = await getCompanyById(user.userId, companyId);

    if (!company) {
      res.status(404).json({
        success: false,
        error: 'Company not found',
        data: {
          companyId,
          message: 'Company not found or you do not have access to it',
        },
      });
      return;
    }

    // Return successful response
    res.status(200).json(
      successResponse('Company retrieved successfully', {
        company,
      })
    );
    return;
  } catch (error) {
    next(handleError(error));
  }
};

export const getCompanyByIdController = asyncErrorHandler(getCompanyByIdHandler);

/**
 * Controller: Disconnect company's Xero connection
 * Removes all Xero tokens and sets connection status to DISCONNECTED
 *
 * @param req.params.id - Company UUID
 * @returns Updated company data with disconnected status
 */
const disconnectCompanyXeroHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get user from authenticated request
    const user = req.user as { userId: string; email?: string } | undefined;
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    // Validate company ID parameter
    const validationResult = companyIdSchema.safeParse(req.params);

    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid company ID',
        data: {
          validationErrors: validationResult.error.errors,
        },
      });
      return;
    }

    const { id: companyId } = validationResult.data;

    // Disconnect Xero connection
    const updatedCompany = await disconnectCompanyXero(user.userId, companyId);

    // Return successful response
    res.status(200).json(
      successResponse('Xero connection disconnected successfully', {
        company: updatedCompany,
        message: 'Company has been disconnected from Xero. All Xero tokens have been removed.',
      })
    );
    return;
  } catch (error) {
    next(handleError(error));
  }
};

export const disconnectCompanyXeroController = asyncErrorHandler(disconnectCompanyXeroHandler);

/**
 * Controller: Reconnect company's Xero connection
 * Initiates OAuth flow for reconnecting a disconnected Xero connection
 *
 * @param req.params.id - Company UUID
 * @returns Xero authorization URL for reconnection
 */
const reconnectCompanyXeroHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Get user from authenticated request
    const user = req.user as { userId: string; email?: string } | undefined;
    if (!user?.userId) {
      throw new Error('User authentication required');
    }

    // Validate company ID parameter
    const validationResult = companyIdSchema.safeParse(req.params);

    if (!validationResult.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid company ID',
        data: {
          validationErrors: validationResult.error.errors,
        },
      });
      return;
    }

    const { id: companyId } = validationResult.data;

    // Initiate reconnection process
    const reconnectionData = await reconnectCompanyXero(user.userId, companyId);

    // Return successful response with authorization URL
    res.status(200).json(
      successResponse('Xero reconnection initiated successfully', {
        authorizationUrl: reconnectionData.authorizationUrl,
        companyId,
        message: 'Please visit the authorization URL to reconnect your Xero account.',
        instructions:
          'After completing the authorization, your company will be reconnected to Xero.',
      })
    );
    return;
  } catch (error) {
    next(handleError(error));
  }
};

export const reconnectCompanyXeroController = asyncErrorHandler(reconnectCompanyXeroHandler);
