
/**
 * Maps each allowed entity to its Lambda function URL
 * pulled from config.LAMBDA_ENDPOINTironment variables for security
 */
import { config } from '@config/config'

export const lambdaEntityUrlMap: Record<string, string> = {
    "Bank Transactions": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_BANK_TRANSACTIONS_URL!,
    "Bank Transfers": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_BANK_TRANSFERS_URL!,
    "Credit Notes": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_CREDIT_NOTES_URL!,
    "Employees": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_EMPLOYEES_URL!,
    "Expense Claims": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_EXPENSE_CLAIMS_URL!,
    "Invoices": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_INVOICES_URL!,
    "Journals": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_JOURNALS_URL!,
    "Manual Journals": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_MANUAL_JOURNALS_URL!,
    "Payments": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_PAYMENTS_URL!,
    "Tracking Categories": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_TRACKING_CATEGORIES_URL!,
    "Tax Rates": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_TAX_RATES_URL!,
    "Attachments": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_ATTACHMENTS_URL!,
    "ProfitLoss": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_PL_TRACKING_URL!,

    "Reports": config.LAMBDA_ENDPOINT.LAMBDA_BALANCE_SHEET!,
    "BalanceSheet": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_BS_NO_TRACKING_URL!,
    "TrialBalance": config.LAMBDA_ENDPOINT.LAMBDA_SYNC_TRIAL_BALANCE_URL!,
};
