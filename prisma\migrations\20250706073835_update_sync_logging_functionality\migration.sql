/*
  Warnings:

  - You are about to drop the column `LastSync` on the `SyncLog` table. All the data in the column will be lost.
  - The `Status` column on the `SyncLog` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - Added the required column `Integration` to the `SyncLog` table without a default value. This is not possible if the table is not empty.
  - Added the required column `UpdatedAt` to the `SyncLog` table without a default value. This is not possible if the table is not empty.

*/
-- <PERSON>reateEnum
CREATE TYPE "SyncStatus" AS ENUM ('PENDING', 'IN_PROGRESS', 'SUCCESS', 'WARNING', 'ERROR', 'RETRYING', 'CANCELLED');

-- AlterTable
ALTER TABLE "Company" ADD COLUMN     "LastSyncDate" TIMESTAMP(3),
ADD COLUMN     "NextSyncDate" TIMESTAMP(3);

-- AlterTable
ALTER TABLE "SyncLog" DROP COLUMN "LastSync",
ADD COLUMN     "ApiEndpoint" VARCHAR(200),
ADD COLUMN     "CompletedAt" TIMESTAMP(3),
ADD COLUMN     "Duration" VARCHAR(20),
ADD COLUMN     "ErrorDetails" JSONB,
ADD COLUMN     "Integration" VARCHAR(50) NOT NULL,
ADD COLUMN     "LastRetryAt" TIMESTAMP(3),
ADD COLUMN     "MaxRetries" INTEGER NOT NULL DEFAULT 3,
ADD COLUMN     "Message" TEXT,
ADD COLUMN     "Method" VARCHAR(10),
ADD COLUMN     "NextRetryAt" TIMESTAMP(3),
ADD COLUMN     "RequestId" VARCHAR(100),
ADD COLUMN     "RequestPayload" JSONB,
ADD COLUMN     "ResponsePayload" JSONB,
ADD COLUMN     "RetryCount" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "StartedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "UpdatedAt" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "UserId" UUID,
DROP COLUMN "Status",
ADD COLUMN     "Status" "SyncStatus" NOT NULL DEFAULT 'PENDING';

-- AddForeignKey
ALTER TABLE "SyncLog" ADD CONSTRAINT "SyncLog_UserId_fkey" FOREIGN KEY ("UserId") REFERENCES "User"("Id") ON DELETE SET NULL ON UPDATE CASCADE;
