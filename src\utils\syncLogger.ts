/**
 * @fileoverview Sync Logger Utility
 * @description Utility functions for logging sync operations with automatic
 * retry handling and performance tracking. Provides a simple interface
 * for integrating sync logging into existing services.
 *
 * Key Features:
 * - Automatic sync log creation and updates
 * - Performance timing and duration tracking
 * - Error handling and retry scheduling
 * - Request/response payload logging
 * - Integration with existing sync services
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024-01-01
 */

import { SyncStatus } from '@prisma/client';
import {
  createSyncLog,
  updateSyncLog,
  scheduleRetry,
  CreateSyncLogRequest,
} from '@services/syncLog.service';
import logger from '@utils/logger';

/**
 * Interface for sync operation context
 */
export interface SyncContext {
  syncLogId: string;
  startTime: number;
  entity: string;
  integration: string;
  companyId: string;
  userId?: string;
}

/**
 * Interface for sync operation result
 */
export interface SyncResult {
  success: boolean;
  data?: any;
  error?: Error;
  warning?: string;
  recordsProcessed?: number;
}

/**
 * Start a sync operation and create a sync log entry
 * @param request - Sync log creation request
 * @returns Promise with sync context
 */
export const startSyncOperation = async (request: CreateSyncLogRequest): Promise<SyncContext> => {
  try {
    const syncLog = await createSyncLog(request);

    const context: SyncContext = {
      syncLogId: syncLog.Id,
      startTime: Date.now(),
      entity: request.entity,
      integration: request.integration,
      companyId: request.companyId,
      userId: request.userId,
    };

    // Update status to IN_PROGRESS
    await updateSyncLog({
      id: syncLog.Id,
      status: SyncStatus.IN_PROGRESS,
      message: `${request.entity} sync started`,
    });

    logger.info('Sync operation started', {
      syncLogId: syncLog.Id,
      entity: request.entity,
      integration: request.integration,
      companyId: request.companyId,
    });

    return context;
  } catch (error) {
    logger.error('Failed to start sync operation', {
      request,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * Complete a sync operation with success status
 * @param context - Sync operation context
 * @param result - Sync operation result
 * @returns Promise with completion status
 */
export const completeSyncOperation = async (
  context: SyncContext,
  result: SyncResult
): Promise<void> => {
  try {
    const duration = Date.now() - context.startTime;
    const durationStr = `${duration}ms`;

    let status: SyncStatus;
    let message: string;

    if (result.success) {
      status = result.warning ? SyncStatus.WARNING : SyncStatus.SUCCESS;
      message = result.warning || `${context.entity} sync completed successfully`;
      if (result.recordsProcessed) {
        message += ` (${result.recordsProcessed} records processed)`;
      }
    } else {
      status = SyncStatus.ERROR;
      message = result.error?.message || `${context.entity} sync failed`;
    }

    await updateSyncLog({
      id: context.syncLogId,
      status,
      message,
      duration: durationStr,
      responsePayload: result.data,
      errorDetails: result.error ? {
        name: result.error.name,
        message: result.error.message,
        stack: result.error.stack,
      } : undefined,
    });

    logger.info('Sync operation completed', {
      syncLogId: context.syncLogId,
      entity: context.entity,
      status,
      duration: durationStr,
      success: result.success,
    });
  } catch (error) {
    logger.error('Failed to complete sync operation', {
      context,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * Handle sync operation failure with automatic retry scheduling
 * @param context - Sync operation context
 * @param error - Error that caused the failure
 * @param shouldRetry - Whether to schedule a retry
 * @returns Promise with failure handling result
 */
export const handleSyncFailure = async (
  context: SyncContext,
  error: Error,
  shouldRetry: boolean = true
): Promise<{ retryScheduled: boolean }> => {
  try {
    const duration = Date.now() - context.startTime;
    const durationStr = `${duration}ms`;

    const errorDetails = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    };

    if (shouldRetry) {
      // Schedule retry
      await scheduleRetry(context.syncLogId, errorDetails);

      logger.warn('Sync operation failed, retry scheduled', {
        syncLogId: context.syncLogId,
        entity: context.entity,
        error: error.message,
        duration: durationStr,
      });

      return { retryScheduled: true };
    } else {
      // Mark as failed without retry
      await updateSyncLog({
        id: context.syncLogId,
        status: SyncStatus.ERROR,
        message: `${context.entity} sync failed: ${error.message}`,
        duration: durationStr,
        errorDetails,
      });

      logger.error('Sync operation failed, no retry scheduled', {
        syncLogId: context.syncLogId,
        entity: context.entity,
        error: error.message,
        duration: durationStr,
      });

      return { retryScheduled: false };
    }
  } catch (logError) {
    logger.error('Failed to handle sync failure', {
      context,
      originalError: error.message,
      logError: logError instanceof Error ? logError.message : 'Unknown error',
    });
    throw logError;
  }
};

/**
 * Wrapper function to execute a sync operation with automatic logging
 * @param request - Sync log creation request
 * @param operation - Async function that performs the sync operation
 * @returns Promise with operation result
 */
export const withSyncLogging = async <T>(
  request: CreateSyncLogRequest,
  operation: (context: SyncContext) => Promise<T>
): Promise<T> => {
  let context: SyncContext | null = null;

  try {
    // Start sync operation
    context = await startSyncOperation(request);

    // Execute the operation
    const result = await operation(context);

    // Complete successfully
    await completeSyncOperation(context, {
      success: true,
      data: result,
    });

    return result;
  } catch (error) {
    if (context) {
      // Handle failure with retry
      await handleSyncFailure(context, error as Error, true);
    }
    throw error;
  }
};

/**
 * Update company sync dates after successful sync
 * @param companyId - Company ID to update
 * @param entity - Entity that was synced
 * @returns Promise with update result
 */
export const updateCompanySyncDates = async (
  companyId: string,
  entity: string
): Promise<void> => {
  try {
    const { prisma } = await import('@config/config');

    const now = new Date();

    await prisma.company.update({
      where: { Id: companyId },
      data: {
        LastSyncDate: now,
        // Set next sync date to 24 hours from now (can be customized per entity)
        NextSyncDate: new Date(now.getTime() + 24 * 60 * 60 * 1000),
      },
    });

    logger.info('Company sync dates updated', {
      companyId,
      entity,
      lastSyncDate: now,
    });
  } catch (error) {
    logger.error('Failed to update company sync dates', {
      companyId,
      entity,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    // Don't throw error as this is not critical for sync operation
  }
};
