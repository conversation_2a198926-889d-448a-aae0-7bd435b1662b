import jwt, { JwtPayload, SignOptions } from 'jsonwebtoken';
import { config } from '@config/config';

/**
 * Utility module to generate and verify JWT tokens
 * for user authentication across the application.
 *
 * This module strictly pulls secrets from the validated
 * configuration and never hardcodes them.
 */

// Defensive check to ensure the secret is always present
if (!config.JWT.SECRET) {
  throw new Error('JWT_SECRET is not defined in environment variables');
}

/**
 * Generates a signed JWT token with a specified payload
 *
 * @param payload - object to embed in the token (e.g., user id, email)
 * @param expiresIn - optional expiry string (defaults to 1 hour)
 * @returns signed JWT token string
 */
export const generateToken = (
  payload: object,
  expiresIn: SignOptions['expiresIn'] = '1h'
): string => {
  const options: SignOptions = { expiresIn };

  return jwt.sign(payload, config.JWT.SECRET, options);
};

/**
 * Verifies and decodes a JWT token
 *
 * @param token - JWT token string to verify
 * @returns decoded payload if valid (JwtPayload), or throws an error
 */
export const verifyToken = (token: string): JwtPayload => {
  // jwt.verify returns string | JwtPayload
  const decoded = jwt.verify(token, config.JWT.SECRET);

  if (typeof decoded === 'string') {
    throw new Error('Invalid token payload structure');
  }

  return decoded;
};
