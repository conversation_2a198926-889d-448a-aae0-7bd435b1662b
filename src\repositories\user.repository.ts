import { prisma } from '@config/config';
import logger from '@utils/logger';
import { UserCreateData, UserUpdateData, UserEntity, SafeUserEntity } from '@models/dto/user.dto';

/**
 * Creates a new user in the database
 *
 * @param data - User creation data with hashed password
 * @returns Promise resolving to the created user entity
 * @throws {Error} If user creation fails
 *
 * @example
 * ```typescript
 * const userData: UserCreateData = {
 *   Email: '<EMAIL>',
 *   Password: 'hashedPassword123',
 *   Name: '<PERSON>'
 * };
 * const user = await createUser(userData);
 * ```
 */
export const createUser = async (data: UserCreateData): Promise<UserEntity> => {
  logger.debug('Creating new user', {
    email: data.Email,
    hasName: !!data.Name,
    isActive: data.IsActive ?? true,
    isVerified: data.IsVerified ?? false,
  });

  try {
    const user = await prisma.user.create({
      data: {
        ...data,
        IsActive: data.IsActive ?? true,
        IsVerified: data.IsVerified ?? false,
      },
    });

    logger.info('User created successfully', {
      userId: user.Id,
      email: user.Email,
    });

    return user as UserEntity;
  } catch (error) {
    logger.error('Failed to create user', {
      email: data.Email,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * Finds a user by email address
 *
 * @param email - User email address to search for
 * @returns Promise resolving to user entity or null if not found
 *
 * @example
 * ```typescript
 * const user = await findUserByEmail('<EMAIL>');
 * if (user) {
 *   console.log('User found:', user.Name);
 * }
 * ```
 */
export const findUserByEmail = async (email: string): Promise<UserEntity | null> => {
  logger.debug('Finding user by email', { email });

  try {
    const user = await prisma.user.findUnique({
      where: { Email: email },
    });

    if (user) {
      logger.debug('User found by email', { userId: user.Id, email });
    } else {
      logger.debug('No user found with email', { email });
    }

    return user as UserEntity | null;
  } catch (error) {
    logger.error('Failed to find user by email', {
      email,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * Updates user logout status by clearing refresh token and expiry
 *
 * @param userId - User ID to logout
 * @returns Promise resolving to updated user entity
 * @throws {Error} If user not found or update fails
 *
 * @example
 * ```typescript
 * const loggedOutUser = await logoutUser('user-uuid-123');
 * console.log('User logged out:', loggedOutUser.Email);
 * ```
 */
export const logoutUser = async (userId: string): Promise<UserEntity> => {
  logger.debug('Logging out user', { userId });

  try {
    const user = await prisma.user.update({
      where: { Id: userId },
      data: {
        RefreshToken: null,
        TokenExpiry: null,
        LastLoginAt: new Date(), // Update last login to track logout time
      },
    });

    logger.info('User logged out successfully', { userId, email: user.Email });
    return user as UserEntity;
  } catch (error) {
    logger.error('Failed to logout user', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * Finds a user by ID, returning only safe fields (no sensitive data)
 *
 * @param userId - User ID to search for
 * @returns Promise resolving to safe user data or null if not found
 *
 * @example
 * ```typescript
 * const safeUser = await findUserById('user-uuid-123');
 * if (safeUser) {
 *   // Safe to return to client - no password or tokens
 *   res.json(safeUser);
 * }
 * ```
 */
export const findUserById = async (userId: string): Promise<SafeUserEntity | null> => {
  logger.debug('Finding user by ID', { userId });

  try {
    const user = await prisma.user.findUnique({
      where: { Id: userId },
      select: {
        Id: true,
        Name: true,
        Email: true,
        IsActive: true,
        IsVerified: true,
        CreatedAt: true,
        UpdatedAt: true,
        LastLoginAt: true,
      },
    });

    if (user) {
      logger.debug('User found by ID', { userId, email: user.Email });
    } else {
      logger.debug('No user found with ID', { userId });
    }

    return user as SafeUserEntity | null;
  } catch (error) {
    logger.error('Failed to find user by ID', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};

/**
 * Updates user data with partial update support
 *
 * @param userId - User ID to update
 * @param data - Partial user data to update
 * @returns Promise resolving to updated user entity
 * @throws {Error} If user not found or update fails
 *
 * @example
 * ```typescript
 * const updatedUser = await updateUser('user-uuid-123', {
 *   Name: 'New Name',
 *   IsVerified: true
 * });
 * ```
 */
export const updateUser = async (userId: string, data: UserUpdateData): Promise<UserEntity> => {
  logger.debug('Updating user', { userId, updateFields: Object.keys(data) });

  try {
    const user = await prisma.user.update({
      where: { Id: userId },
      data,
    });

    logger.info('User updated successfully', {
      userId,
      email: user.Email,
      updatedFields: Object.keys(data),
    });

    return user as UserEntity;
  } catch (error) {
    logger.error('Failed to update user', {
      userId,
      updateFields: Object.keys(data),
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    throw error;
  }
};
