-- CreateTable
CREATE TABLE "ProfitLoss" (
    "Id" UUID NOT NULL,
    "Year" INTEGER NOT NULL,
    "Month" INTEGER NOT NULL,
    "AccountId" TEXT NOT NULL,
    "AccountName" TEXT NOT NULL,
    "Amount" DECIMAL(18,2) NOT NULL,
    "CompanyId" UUID NOT NULL,

    CONSTRAINT "ProfitLoss_pkey" PRIMARY KEY ("Id")
);

-- AddForeignKey
ALTER TABLE "ProfitLoss" ADD CONSTRAINT "ProfitLoss_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;
